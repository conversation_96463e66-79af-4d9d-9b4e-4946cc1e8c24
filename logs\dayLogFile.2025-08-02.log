2025-08-02 09:50:49.543 INFO  [background-preinit] o.h.v.i.u.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-08-02 09:50:49.673 INFO  [restartedMain] c.e.s.SpringbootApplication - Starting SpringbootApplication using Java 17.0.6 with PID 33896 (D:\cursorProjects\MyWenJuanXing_Management2\target\classes started by ZhangYangyang in D:\cursorProjects\MyWenJuanXing_Management2)
2025-08-02 09:50:49.674 DEBUG [restartedMain] c.e.s.SpringbootApplication - Running with Spring Boot v3.4.3, Spring v6.2.3
2025-08-02 09:50:49.676 INFO  [restartedMain] c.e.s.SpringbootApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-02 09:50:50.133 INFO  [restartedMain] o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-08-02 09:50:50.134 INFO  [restartedMain] o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-08-02 09:50:51.603 INFO  [restartedMain] o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-02 09:50:51.825 INFO  [restartedMain] o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 197 ms. Found 1 JPA repository interface.
2025-08-02 09:50:54.280 INFO  [restartedMain] o.s.b.w.e.t.TomcatWebServer - Tomcat initialized with port 9090 (http)
2025-08-02 09:50:54.328 INFO  [restartedMain] o.a.c.h.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9090"]
2025-08-02 09:50:54.335 INFO  [restartedMain] o.a.c.c.StandardService - Starting service [Tomcat]
2025-08-02 09:50:54.336 INFO  [restartedMain] o.a.c.c.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.36]
2025-08-02 09:50:54.516 INFO  [restartedMain] o.a.c.c.C.[.[.[/] - Initializing Spring embedded WebApplicationContext
2025-08-02 09:50:54.517 INFO  [restartedMain] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4381 ms
2025-08-02 09:50:54.874 INFO  [restartedMain] c.z.h.HikariDataSource - HikariPool-1 - Starting...
2025-08-02 09:50:55.867 INFO  [restartedMain] c.z.h.p.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@5fb053cd
2025-08-02 09:50:55.876 INFO  [restartedMain] c.z.h.HikariDataSource - HikariPool-1 - Start completed.
2025-08-02 09:50:56.357 INFO  [restartedMain] o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-02 09:50:56.454 INFO  [restartedMain] o.h.Version - HHH000412: Hibernate ORM core version 6.6.8.Final
2025-08-02 09:50:56.496 INFO  [restartedMain] o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-08-02 09:50:56.832 INFO  [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-02 09:50:56.999 INFO  [restartedMain] o.h.o.c.pooling - HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource 'HikariDataSource (HikariPool-1)']
	Database driver: undefined/unknown
	Database version: 8.0.36
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
2025-08-02 09:50:58.025 INFO  [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-02 09:50:58.028 INFO  [restartedMain] o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-02 09:51:00.679 WARN  [restartedMain] o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-08-02 09:51:01.044 INFO  [restartedMain] o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page template: index
2025-08-02 09:51:02.995 INFO  [restartedMain] o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-08-02 09:51:03.067 INFO  [restartedMain] o.s.m.s.b.SimpleBrokerMessageHandler - Starting...
2025-08-02 09:51:03.068 INFO  [restartedMain] o.s.m.s.b.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@14cdf3e9]]
2025-08-02 09:51:03.071 INFO  [restartedMain] o.s.m.s.b.SimpleBrokerMessageHandler - Started.
2025-08-02 09:51:03.089 INFO  [restartedMain] o.s.b.w.e.t.TomcatWebServer - Tomcat started on port 9090 (http) with context path '/'
2025-08-02 09:51:03.103 INFO  [restartedMain] c.e.s.SpringbootApplication - Started SpringbootApplication in 14.043 seconds (process running for 15.128)
2025-08-02 09:51:03.107 INFO  [SpringApplicationShutdownHook] o.s.b.w.e.t.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-02 09:51:03.720 INFO  [tomcat-shutdown] o.s.b.w.e.t.GracefulShutdown - Graceful shutdown complete
2025-08-02 09:51:03.722 INFO  [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler - Stopping...
2025-08-02 09:51:03.722 INFO  [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@14cdf3e9]]
2025-08-02 09:51:03.722 INFO  [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler - Stopped.
2025-08-02 09:51:03.724 INFO  [SpringApplicationShutdownHook] o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-02 09:51:03.728 INFO  [SpringApplicationShutdownHook] c.z.h.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-08-02 09:51:03.734 INFO  [SpringApplicationShutdownHook] c.z.h.HikariDataSource - HikariPool-1 - Shutdown completed.
