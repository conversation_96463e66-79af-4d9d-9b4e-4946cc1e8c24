package com.example.springboot.tool;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.math3.linear.Array2DRowRealMatrix;
import org.apache.commons.math3.distribution.NormalDistribution;
import org.apache.commons.math3.linear.LUDecomposition;
import org.apache.commons.math3.linear.RealMatrix;
import org.apache.commons.math3.stat.correlation.PearsonsCorrelation;
import org.apache.commons.math3.stat.descriptive.DescriptiveStatistics;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.ai.tool.annotation.ToolParam;
import org.springframework.stereotype.Component;

import com.example.springboot.entity.SurveyData;
import com.example.springboot.entity.WjxSurveyData;
import com.example.springboot.service.AIChatService;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 逆向数据调整工具类高级功能
 * 包含中介效应、调节效应、因子分析等高级调整功能
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class ReverseDataAdjustmentToolsAdvanced {

    private final ReverseDataAdjustmentTools baseTools;
    private final AIChatService aiChatService;

    // 缓存题目量表级数信息，避免重复查询
    private Map<Integer, Integer> questionScaleLevelsCache = new HashMap<>();

    // 当前使用的量表级数
    private Integer currentScaleLevel = 5;

    /**
     * 调整数据以符合中介效应模型
     */
    @Tool(description = "调整数据以符合指定的中介效应模型，支持平行中介和链式中介")
    public String adjustDataForMediationEffect(
            @ToolParam(description = "会话ID") String sessionId,
            @ToolParam(description = "因变量列索引（从1开始）") Integer dependentVar,
            @ToolParam(description = "自变量列索引列表（从1开始）") List<Integer> independentVars,
            @ToolParam(description = "中介变量列索引列表（从1开始）") List<Integer> mediatorVars,
            @ToolParam(description = "中介类型：parallel（平行中介）或chain（链式中介）") String mediationType,
            @ToolParam(description = "目标直接效应系数") Double targetDirectEffect,
            @ToolParam(description = "目标间接效应系数") Double targetIndirectEffect,
            @ToolParam(description = "允许的误差范围，默认0.05") Double tolerance) {

        log.info("[中介效应调整] 开始调整数据，sessionId={}, mediationType={}, targetDirect={}, targetIndirect={}",
                sessionId, mediationType, targetDirectEffect, targetIndirectEffect);

        try {
            if (tolerance == null) tolerance = 0.05;

            // 获取原始数据
            List<Double> yData = baseTools.getNumericColumnData(sessionId, dependentVar - 1);
            List<List<Double>> xData = new ArrayList<>();
            for (Integer col : independentVars) {
                xData.add(baseTools.getNumericColumnData(sessionId, col - 1));
            }
            List<List<Double>> mData = new ArrayList<>();
            for (Integer col : mediatorVars) {
                mData.add(baseTools.getNumericColumnData(sessionId, col - 1));
            }

            if (yData.isEmpty() || xData.isEmpty() || mData.isEmpty()) {
                throw new RuntimeException("数据为空");
            }

            List<List<Object>> changedCells = new ArrayList<>();

            if ("parallel".equals(mediationType)) {
                // 平行中介效应调整
                changedCells = adjustParallelMediationEffect(sessionId, yData, xData, mData,
                    dependentVar, independentVars, mediatorVars, targetDirectEffect, targetIndirectEffect);
            } else if ("chain".equals(mediationType)) {
                // 链式中介效应调整
                changedCells = adjustChainMediationEffect(sessionId, yData, xData, mData,
                    dependentVar, independentVars, mediatorVars, targetDirectEffect, targetIndirectEffect);
            } else {
                throw new IllegalArgumentException("不支持的中介类型：" + mediationType);
            }

            Map<String, Object> metrics = new HashMap<>();
            metrics.put("mediationType", mediationType);
            metrics.put("targetDirectEffect", targetDirectEffect);
            metrics.put("targetIndirectEffect", targetIndirectEffect);
            metrics.put("cellsChanged", changedCells.size());

            // 生成调整说明，但不保存到数据库（让AI聊天服务统一处理）
            String explanation = String.format(
                "成功调整中介效应模型，目标直接效应%.3f，间接效应%.3f，共调整%d个单元格。",
                targetDirectEffect, targetIndirectEffect, changedCells.size()
            );

            // 保存调整后的数据到数据库，并返回分析结果给AI
            return baseTools.saveAdjustedDataToMessage(sessionId, changedCells, explanation, metrics);

        } catch (Exception e) {
            log.error("[中介效应调整] 调整失败", e);
            return "中介效应调整失败: " + e.getMessage();
        }
    }

    /**
     * 调整平行中介效应
     */
    private List<List<Object>> adjustParallelMediationEffect(String sessionId, List<Double> yData,
            List<List<Double>> xData, List<List<Double>> mData, Integer dependentVar,
            List<Integer> independentVars, List<Integer> mediatorVars,
            Double targetDirectEffect, Double targetIndirectEffect) {

        List<List<Object>> changedCells = new ArrayList<>();
        int sampleSize = yData.size();

        // 调整中介变量以产生目标间接效应
        for (int i = 0; i < mediatorVars.size(); i++) {
            List<Double> adjustedMediatorData = generateMediatorData(xData.get(0), targetIndirectEffect / mediatorVars.size());

            // 生成变更记录
            for (int j = 0; j < sampleSize; j++) {
                if (!mData.get(i).get(j).equals(adjustedMediatorData.get(j))) {
                    double adjustedValue = adjustedMediatorData.get(j);
                    // 根据中介变量的量表级数动态确定范围
                    Integer scaleLevel = getQuestionScaleLevel(mediatorVars.get(i), mData.get(i));
                    int optionValue = Math.max(1, Math.min(scaleLevel, (int) Math.round(adjustedValue)));

                    changedCells.add(Arrays.asList(
                        j + 1,
                        mediatorVars.get(i) - 1,
                        String.valueOf(optionValue)
                    ));
                }
            }
        }

        // 调整因变量以产生目标直接效应
        List<Double> adjustedYData = generateDependentVariableWithMediationEffect(
            xData, mData, targetDirectEffect, targetIndirectEffect);

        for (int i = 0; i < sampleSize; i++) {
            if (!yData.get(i).equals(adjustedYData.get(i))) {
                double adjustedValue = adjustedYData.get(i);
                // 根据因变量的量表级数动态确定范围
                Integer scaleLevel = getQuestionScaleLevel(dependentVar, yData);
                int optionValue = Math.max(1, Math.min(scaleLevel, (int) Math.round(adjustedValue)));

                changedCells.add(Arrays.asList(
                    i + 1,
                    dependentVar - 1,
                    String.valueOf(optionValue)
                ));
            }
        }

        return changedCells;
    }

    /**
     * 调整链式中介效应
     */
    private List<List<Object>> adjustChainMediationEffect(String sessionId, List<Double> yData,
            List<List<Double>> xData, List<List<Double>> mData, Integer dependentVar,
            List<Integer> independentVars, List<Integer> mediatorVars,
            Double targetDirectEffect, Double targetIndirectEffect) {

        List<List<Object>> changedCells = new ArrayList<>();
        int sampleSize = yData.size();

        // 链式中介：X -> M1 -> M2 -> ... -> Y
        List<List<Double>> adjustedMediators = new ArrayList<>();

        // 第一个中介变量基于自变量调整
        List<Double> firstMediator = generateMediatorData(xData.get(0), 0.5);
        adjustedMediators.add(firstMediator);

        // 后续中介变量基于前一个中介变量调整
        for (int i = 1; i < mediatorVars.size(); i++) {
            List<Double> nextMediator = generateMediatorData(adjustedMediators.get(i-1), 0.5);
            adjustedMediators.add(nextMediator);
        }

        // 生成中介变量的变更记录
        for (int i = 0; i < mediatorVars.size(); i++) {
            for (int j = 0; j < sampleSize; j++) {
                if (!mData.get(i).get(j).equals(adjustedMediators.get(i).get(j))) {
                    double adjustedValue = adjustedMediators.get(i).get(j);
                    // 根据中介变量的量表级数动态确定范围
                    Integer scaleLevel = getQuestionScaleLevel(mediatorVars.get(i), mData.get(i));
                    int optionValue = Math.max(1, Math.min(scaleLevel, (int) Math.round(adjustedValue)));

                    changedCells.add(Arrays.asList(
                        j + 1,
                        mediatorVars.get(i) - 1,
                        String.valueOf(optionValue)
                    ));
                }
            }
        }

        // 调整因变量
        List<Double> adjustedYData = generateDependentVariableWithChainMediation(
            xData, adjustedMediators, targetDirectEffect, targetIndirectEffect);

        for (int i = 0; i < sampleSize; i++) {
            if (!yData.get(i).equals(adjustedYData.get(i))) {
                double adjustedValue = adjustedYData.get(i);
                // 根据因变量的量表级数动态确定范围
                Integer scaleLevel = getQuestionScaleLevel(dependentVar, yData);
                int optionValue = Math.max(1, Math.min(scaleLevel, (int) Math.round(adjustedValue)));

                changedCells.add(Arrays.asList(
                    i + 1,
                    dependentVar - 1,
                    String.valueOf(optionValue)
                ));
            }
        }

        return changedCells;
    }

    /**
     * 生成中介变量数据
     */
    private List<Double> generateMediatorData(List<Double> predictor, double effectSize) {
        List<Double> result = new ArrayList<>();
        NormalDistribution errorDist = new NormalDistribution(0, 0.5);

        DescriptiveStatistics stats = new DescriptiveStatistics();
        predictor.forEach(stats::addValue);
        double mean = stats.getMean();
        double std = stats.getStandardDeviation();

        for (Double x : predictor) {
            double standardizedX = (x - mean) / std;
            double mediatorValue = effectSize * standardizedX + errorDist.sample();
            result.add(mediatorValue);
        }

        return result;
    }

    /**
     * 生成具有中介效应的因变量数据
     */
    private List<Double> generateDependentVariableWithMediationEffect(List<List<Double>> xData,
            List<List<Double>> mData, double directEffect, double indirectEffect) {

        List<Double> result = new ArrayList<>();
        int sampleSize = xData.get(0).size();
        NormalDistribution errorDist = new NormalDistribution(0, 0.3);

        for (int i = 0; i < sampleSize; i++) {
            double yValue = 0.0;

            // 直接效应
            yValue += directEffect * xData.get(0).get(i);

            // 间接效应（通过中介变量）
            for (List<Double> mediator : mData) {
                yValue += (indirectEffect / mData.size()) * mediator.get(i);
            }

            // 添加误差项
            yValue += errorDist.sample();

            result.add(yValue);
        }

        return result;
    }

    /**
     * 生成具有链式中介效应的因变量数据
     */
    private List<Double> generateDependentVariableWithChainMediation(List<List<Double>> xData,
            List<List<Double>> mData, double directEffect, double indirectEffect) {

        List<Double> result = new ArrayList<>();
        int sampleSize = xData.get(0).size();
        NormalDistribution errorDist = new NormalDistribution(0, 0.3);

        for (int i = 0; i < sampleSize; i++) {
            double yValue = 0.0;

            // 直接效应
            yValue += directEffect * xData.get(0).get(i);

            // 链式间接效应（通过最后一个中介变量）
            if (!mData.isEmpty()) {
                yValue += indirectEffect * mData.get(mData.size() - 1).get(i);
            }

            // 添加误差项
            yValue += errorDist.sample();

            result.add(yValue);
        }

        return result;
    }

    /**
     * 调整数据以符合调节效应模型
     */
    @Tool(description = "调整数据以符合指定的调节效应模型，控制交互项的显著性")
    public String adjustDataForModerationEffect(
            @ToolParam(description = "会话ID") String sessionId,
            @ToolParam(description = "因变量列索引（从1开始）") Integer dependentVar,
            @ToolParam(description = "自变量列索引列表（从1开始）") List<Integer> independentVars,
            @ToolParam(description = "调节变量列索引列表（从1开始）") List<Integer> moderatorVars,
            @ToolParam(description = "目标主效应系数列表") List<Double> targetMainEffects,
            @ToolParam(description = "目标交互效应系数") Double targetInteractionEffect,
            @ToolParam(description = "允许的误差范围，默认0.05") Double tolerance) {

        log.info("[调节效应调整] 开始调整数据，sessionId={}, dependentVar={}, targetInteraction={}",
                sessionId, dependentVar, targetInteractionEffect);

        try {
            if (tolerance == null) tolerance = 0.05;

            // 获取原始数据
            List<Double> yData = baseTools.getNumericColumnData(sessionId, dependentVar - 1);
            List<List<Double>> xData = new ArrayList<>();
            for (Integer col : independentVars) {
                xData.add(baseTools.getNumericColumnData(sessionId, col - 1));
            }
            List<List<Double>> zData = new ArrayList<>();
            for (Integer col : moderatorVars) {
                zData.add(baseTools.getNumericColumnData(sessionId, col - 1));
            }

            if (yData.isEmpty() || xData.isEmpty() || zData.isEmpty()) {
                throw new RuntimeException("数据为空");
            }

            // 验证参数
            if (targetMainEffects.size() != independentVars.size() + moderatorVars.size()) {
                throw new IllegalArgumentException("主效应系数数量必须等于自变量和调节变量的总数");
            }

            // 生成具有调节效应的因变量数据
            List<Double> adjustedYData = generateModerationEffectData(xData, zData,
                targetMainEffects, targetInteractionEffect);

            // 生成变更记录
            List<List<Object>> changedCells = new ArrayList<>();
            for (int i = 0; i < yData.size(); i++) {
                if (!yData.get(i).equals(adjustedYData.get(i))) {
                    double adjustedValue = adjustedYData.get(i);
                    // 根据因变量的量表级数动态确定范围
                    Integer scaleLevel = getQuestionScaleLevel(dependentVar, yData);
                    int optionValue = Math.max(1, Math.min(scaleLevel, (int) Math.round(adjustedValue)));

                    changedCells.add(Arrays.asList(
                        i + 1,
                        dependentVar - 1,
                        String.valueOf(optionValue)
                    ));
                }
            }

            Map<String, Object> metrics = new HashMap<>();
            metrics.put("targetMainEffects", targetMainEffects);
            metrics.put("targetInteractionEffect", targetInteractionEffect);
            metrics.put("cellsChanged", changedCells.size());

            // 生成调整说明，但不保存到数据库（让AI聊天服务统一处理）
            String explanation = String.format(
                "成功调整调节效应模型，目标交互效应%.3f，共调整%d个单元格。",
                targetInteractionEffect, changedCells.size()
            );

            // 保存调整后的数据到数据库，并返回分析结果给AI
            return baseTools.saveAdjustedDataToMessage(sessionId, changedCells, explanation, metrics);

        } catch (Exception e) {
            log.error("[调节效应调整] 调整失败", e);
            return "调节效应调整失败: " + e.getMessage();
        }
    }

    /**
     * 生成具有调节效应的数据
     */
    private List<Double> generateModerationEffectData(List<List<Double>> xData, List<List<Double>> zData,
            List<Double> mainEffects, double interactionEffect) {

        List<Double> result = new ArrayList<>();
        int sampleSize = xData.get(0).size();
        NormalDistribution errorDist = new NormalDistribution(0, 0.3);

        for (int i = 0; i < sampleSize; i++) {
            double yValue = 0.0;

            // 主效应 - 自变量
            for (int j = 0; j < xData.size(); j++) {
                yValue += mainEffects.get(j) * xData.get(j).get(i);
            }

            // 主效应 - 调节变量
            for (int j = 0; j < zData.size(); j++) {
                yValue += mainEffects.get(xData.size() + j) * zData.get(j).get(i);
            }

            // 交互效应 (X * Z)
            for (int j = 0; j < xData.size(); j++) {
                for (int k = 0; k < zData.size(); k++) {
                    yValue += interactionEffect * xData.get(j).get(i) * zData.get(k).get(i);
                }
            }

            // 添加误差项
            yValue += errorDist.sample();

            result.add(yValue);
        }

        return result;
    }

    /**
     * 调整数据以改善因子分析结果
     */
    @Tool(description = "调整数据以改善因子分析结果，提高KMO值和因子载荷")
    public String adjustDataForFactorAnalysis(
            @ToolParam(description = "会话ID") String sessionId,
            @ToolParam(description = "需要调整的列索引列表（从1开始）") List<Integer> columns,
            @ToolParam(description = "目标KMO值（0.5-1.0）") Double targetKMO,
            @ToolParam(description = "目标因子载荷阈值（如0.6）") Double targetLoadingThreshold,
            @ToolParam(description = "允许的误差范围，默认0.05") Double tolerance) {

        log.info("[因子分析调整] 开始调整数据，sessionId={}, columns={}, targetKMO={}",
                sessionId, columns, targetKMO);

        try {
            if (tolerance == null) tolerance = 0.05;

            // 获取原始数据
            List<List<Double>> originalData = new ArrayList<>();
            for (Integer col : columns) {
                List<Double> data = baseTools.getNumericColumnData(sessionId, col - 1);
                if (data.isEmpty()) {
                    throw new RuntimeException("第" + col + "列没有有效的数值数据");
                }
                originalData.add(new ArrayList<>(data));
            }

            // 检查数据有效性，特别是同值问题
            if (!isDataValid(originalData)) {
                log.error("[因子分析调整] 原始数据无效，可能存在同值列，先进行数据修复");
                originalData = ensureDataVariability(originalData);
            }

            // 增强变量间相关性以提高KMO值
            List<List<Double>> adjustedData = enhanceCorrelationsForFactorAnalysis(originalData, targetKMO);

            // 最终确保数据变异性
            adjustedData = ensureDataVariability(adjustedData);

            // 生成变更记录
            List<List<Object>> changedCells = new ArrayList<>();
            for (int colIdx = 0; colIdx < columns.size(); colIdx++) {
                List<Double> originalCol = originalData.get(colIdx);
                List<Double> adjustedCol = adjustedData.get(colIdx);

                for (int rowIdx = 0; rowIdx < originalCol.size(); rowIdx++) {
                    if (!originalCol.get(rowIdx).equals(adjustedCol.get(rowIdx))) {
                        double adjustedValue = adjustedCol.get(rowIdx);
                        // 根据列的量表级数动态确定范围
                        Integer scaleLevel = getQuestionScaleLevel(columns.get(colIdx), originalCol);
                        int optionValue = Math.max(1, Math.min(scaleLevel, (int) Math.round(adjustedValue)));

                        changedCells.add(Arrays.asList(
                            rowIdx + 1,
                            columns.get(colIdx) - 1,
                            String.valueOf(optionValue)
                        ));
                    }
                }
            }

            Map<String, Object> metrics = new HashMap<>();
            metrics.put("targetKMO", targetKMO);
            metrics.put("targetLoadingThreshold", targetLoadingThreshold);
            metrics.put("cellsChanged", changedCells.size());

            // 重新计算实际的KMO值，确保信息一致性
            Map<String, Object> actualStats = baseTools.calculateFactorAnalysisInternal(sessionId, columns);
            double actualKMO = (Double) actualStats.getOrDefault("kmo", targetKMO);
            metrics.put("achievedKMO", actualKMO);

            // 生成调整说明，但不保存到数据库（让AI聊天服务统一处理）
            String explanation = String.format(
                "成功调整因子分析KMO值到%.3f（目标%.3f），载荷阈值%.2f，共调整%d个单元格。",
                actualKMO, targetKMO, targetLoadingThreshold, changedCells.size()
            );

            // 保存调整后的数据到数据库，并返回分析结果给AI
            return baseTools.saveAdjustedDataToMessage(sessionId, changedCells, explanation, metrics);

        } catch (Exception e) {
            log.error("[因子分析调整] 调整失败", e);
            return "因子分析调整失败: " + e.getMessage();
        }
    }

    /**
     * 增强变量间相关性以改善因子分析 - 保守版本
     * 防止过度调整导致同值问题
     */
    private List<List<Double>> enhanceCorrelationsForFactorAnalysis(List<List<Double>> data, double targetKMO) {
        List<List<Double>> result = new ArrayList<>();
        int numVars = data.size();
        int sampleSize = data.get(0).size();

        log.info("[因子分析增强] 开始增强相关性，目标KMO: {:.3f}", targetKMO);

        // 保存原始分布特征
        List<Double> originalStds = new ArrayList<>();
        for (List<Double> col : data) {
            DescriptiveStatistics stats = new DescriptiveStatistics();
            col.forEach(stats::addValue);
            originalStds.add(stats.getStandardDeviation());
        }

        // 保守的相关性增强策略
        for (int i = 0; i < numVars; i++) {
            List<Double> adjustedVar = new ArrayList<>();
            List<Double> originalVar = data.get(i);

            for (int j = 0; j < sampleSize; j++) {
                double originalValue = originalVar.get(j);

                // 只对部分数据点进行调整
                if (Math.random() > 0.3) { // 只调整30%的数据点
                    adjustedVar.add(originalValue);
                    continue;
                }

                double adjustment = 0.0;

                // 基于其他变量的加权平均进行调整
                for (int k = 0; k < numVars; k++) {
                    if (k != i) {
                        adjustment += data.get(k).get(j);
                    }
                }
                adjustment /= (numVars - 1);

                // 向共同因子方向轻微调整，增强相关性
                double enhancementFactor = 0.1; // 减小调整强度，防止过度调整
                double adjustedValue = originalValue + enhancementFactor * (adjustment - originalValue);

                // 使用默认的7级量表范围（支持大多数量表类型）
                adjustedValue = Math.max(1.0, Math.min(7.0, adjustedValue));
                adjustedVar.add(adjustedValue);
            }

            result.add(adjustedVar);
        }

        // 检查调整后的分布是否被过度破坏
        for (int i = 0; i < numVars; i++) {
            DescriptiveStatistics newStats = new DescriptiveStatistics();
            result.get(i).forEach(newStats::addValue);

            double newStd = newStats.getStandardDeviation();
            double originalStd = originalStds.get(i);

            if (newStd < originalStd * 0.5) {
                log.warn("[因子分析增强] 第{}列标准差过度减小({:.3f} -> {:.3f})，恢复部分原始数据",
                        i + 1, originalStd, newStd);

                // 恢复一些原始数据点
                List<Double> originalCol = data.get(i);
                List<Double> adjustedCol = result.get(i);

                for (int j = 0; j < sampleSize; j++) {
                    if (Math.random() < 0.4) { // 恢复40%的数据点
                        adjustedCol.set(j, originalCol.get(j));
                    }
                }
            }
        }

        log.info("[因子分析增强] 完成相关性增强");
        return result;
    }

    /**
     * 分维度量表信度效度调整工具
     * 支持用户指定哪几题是一个维度，确保每个维度的信效度可以通过，并且总量表的信度也可以通过
     * 支持设置每个维度题目的得分均值和得分方向
     *
     * 使用示例：
     * 1. 基本用法：adjustDataForMultiDimensionalScale(sessionId, [[1,2,3],[4,5,6]], null, null, null, null, null, null, null)
     * 2. 设置题目均值：adjustDataForMultiDimensionalScale(sessionId, [[1,2,3],[4,5,6]], null, null, null, null, null, [[3.5,3.2,3.8],[4.0,3.9,4.1]], null)
     * 3. 设置得分方向：adjustDataForMultiDimensionalScale(sessionId, [[1,2,3],[4,5,6]], null, null, null, null, null, null, [["positive","negative","positive"],["positive","positive","negative"]])
     * 4. 完整设置：adjustDataForMultiDimensionalScale(sessionId, [[1,2,3],[4,5,6]], [0.8,0.85], 0.9, 0.8, 0.4, 0.02, [[3.5,3.2,3.8],[4.0,3.9,4.1]], [["positive","negative","positive"],["positive","positive","negative"]])
     */
    @Tool(description = "调整分维度量表数据，确保每个维度的信度和效度都能通过，同时保证总量表的信度也能通过。支持设置题目得分均值和得分方向。除了dimensions和scaleLevel参数必须指定外，其他参数都有合理的默认值")
    public String adjustDataForMultiDimensionalScale(
            @ToolParam(description = "会话ID") String sessionId,
            @ToolParam(description = "维度定义，每个维度包含的题目列号列表，格式：[[1,2,3],[4,5,6],[7,8,9]]") List<List<Integer>> dimensions,
            @ToolParam(description = "量表级数，如5表示5级量表(1-5)，7表示7级量表(1-7)") Integer scaleLevel,
            @ToolParam(description = "每个维度的目标信度系数列表，与dimensions对应，不指定时默认为0.8") List<Double> targetDimensionAlphas,
            @ToolParam(description = "总量表的目标信度系数，不指定时默认为0.85") Double targetTotalAlpha,
            @ToolParam(description = "目标KMO值（效度指标，0.6-1.0），不指定时默认为0.8") Double targetKMO,
            @ToolParam(description = "维度间目标相关系数（用于区分效度），不指定时默认为0.4") Double targetInterDimensionCorrelation,
            @ToolParam(description = "允许的误差范围，默认0.02") Double tolerance,
            @ToolParam(description = "每个维度题目的目标得分均值列表，与dimensions对应，格式：[[3.5,3.2,3.8],[4.0,3.9,4.1]]，不指定时不调整均值") List<List<Double>> targetItemMeans,
            @ToolParam(description = "每个维度题目的得分方向列表，与dimensions对应，格式：[['positive','negative','positive'],['positive','positive','negative']]，'positive'表示正向计分（1分最低），'negative'表示反向计分（1分最高），不指定时默认全部为正向") List<List<String>> scoringDirections) {

        log.info("[分维度量表调整] 开始调整数据，sessionId={}, dimensions={}, scaleLevel={}, targetTotalAlpha={}",
                sessionId, dimensions, scaleLevel, targetTotalAlpha);

        try {
            // ========== 第一阶段：参数验证和默认值设置 ==========
            if (tolerance == null) tolerance = 0.02;
            if (targetTotalAlpha == null) targetTotalAlpha = 0.85;
            if (targetKMO == null) targetKMO = 0.8;
            if (targetInterDimensionCorrelation == null) targetInterDimensionCorrelation = 0.4;

            // 验证参数
            if (dimensions == null || dimensions.isEmpty()) {
                throw new IllegalArgumentException("维度定义不能为空，必须指定每个维度包含的题目");
            }

            if (scaleLevel == null || scaleLevel < 2 || scaleLevel > 10) {
                throw new IllegalArgumentException("量表级数必须在2到10之间，当前值：" + scaleLevel);
            }

            // 设置当前使用的量表级数
            this.currentScaleLevel = scaleLevel;

            // 验证数值参数范围
            if (targetTotalAlpha <= 0 || targetTotalAlpha > 1) {
                throw new IllegalArgumentException("总信度系数必须在0到1之间，当前值：" + targetTotalAlpha);
            }
            if (targetTotalAlpha < 0.6) {
                throw new IllegalArgumentException("总信度系数过低（" + targetTotalAlpha + "），建议设置在0.6以上");
            }

            if (targetKMO <= 0 || targetKMO > 1) {
                throw new IllegalArgumentException("KMO值必须在0到1之间，当前值：" + targetKMO);
            }
            if (targetKMO < 0.5) {
                throw new IllegalArgumentException("KMO值过低（" + targetKMO + "），建议设置在0.5以上");
            }

            if (targetInterDimensionCorrelation < 0 || targetInterDimensionCorrelation > 1) {
                throw new IllegalArgumentException("维度间相关系数必须在0到1之间，当前值：" + targetInterDimensionCorrelation);
            }

            if (tolerance <= 0 || tolerance > 0.5) {
                throw new IllegalArgumentException("容差必须在0到0.5之间，当前值：" + tolerance);
            }

            // 为每个维度设置默认信度目标值
            if (targetDimensionAlphas == null || targetDimensionAlphas.isEmpty()) {
                targetDimensionAlphas = new ArrayList<>();
                for (int i = 0; i < dimensions.size(); i++) {
                    targetDimensionAlphas.add(0.8); // 默认每个维度目标信度为0.8
                }
                log.info("[分维度量表调整] 使用默认维度信度目标值: {}", targetDimensionAlphas);
            } else if (targetDimensionAlphas.size() != dimensions.size()) {
                throw new IllegalArgumentException("维度目标信度数量必须与维度数量一致，或者不指定使用默认值0.8");
            }

            // 验证每个维度的信度系数
            for (int i = 0; i < targetDimensionAlphas.size(); i++) {
                Double alpha = targetDimensionAlphas.get(i);
                if (alpha == null || alpha <= 0 || alpha > 1) {
                    throw new IllegalArgumentException("维度" + (i + 1) + "的信度系数必须在0到1之间，当前值：" + alpha);
                }
                if (alpha < 0.5) {
                    throw new IllegalArgumentException("维度" + (i + 1) + "的信度系数过低（" + alpha + "），建议设置在0.6以上");
                }
            }

            // 验证维度定义和题目类型
            Set<Integer> allQuestions = new HashSet<>();
            for (int i = 0; i < dimensions.size(); i++) {
                List<Integer> dimension = dimensions.get(i);
                if (dimension == null || dimension.isEmpty()) {
                    throw new IllegalArgumentException("维度" + (i + 1) + "不能为空");
                }
                if (dimension.size() < 2) {
                    throw new IllegalArgumentException("维度" + (i + 1) + "至少需要2个题目，当前只有" + dimension.size() + "个");
                }
                for (Integer questionNum : dimension) {
                    if (questionNum == null || questionNum <= 0) {
                        throw new IllegalArgumentException("维度" + (i + 1) + "中包含无效的题目编号: " + questionNum);
                    }
                    if (allQuestions.contains(questionNum)) {
                        throw new IllegalArgumentException("题目" + questionNum + "被重复分配到多个维度");
                    }
                    allQuestions.add(questionNum);
                }
            }

            // 获取题目结构信息，用于动态确定量表级数和验证题目类型
            questionScaleLevelsCache.clear(); // 清空缓存
            Map<Integer, String> questionTypesCache = new HashMap<>();
            Map<Integer, Integer> questionScaleLevelsMap = new HashMap<>();
            Map<Integer, Integer> columnToQuestionMap = new HashMap<>(); // 列编号到题目编号的映射
            Map<Integer, SurveyData> questionDataMap = new HashMap<>(); // 题目编号到题目数据的映射

            try {
                WjxSurveyData surveyData = aiChatService.getSurveyDataBySessionId(sessionId);
                if (surveyData != null && surveyData.getJsonData() != null) {
                    for (SurveyData data : surveyData.getJsonData()) {
                        questionTypesCache.put(data.getNumId(), data.getType());
                        questionDataMap.put(data.getNumId(), data);

                        // 建立列编号到题目编号的映射
                        if (data.getColIndices() != null && !data.getColIndices().isEmpty()) {
                            for (Integer colIndex : data.getColIndices()) {
                                columnToQuestionMap.put(colIndex, data.getNumId());
                            }
                        }

                        if (data.getOptions() != null && !data.getOptions().isEmpty()) {
                            int detectedScaleLevel = data.getOptions().size();
                            questionScaleLevelsCache.put(data.getNumId(), detectedScaleLevel);
                            questionScaleLevelsMap.put(data.getNumId(), detectedScaleLevel);
                        } else if (data.getSubQuestions() != null && !data.getSubQuestions().isEmpty()) {
                            // 对于矩阵题，使用第一个子问题的选项数量
                            SurveyData.SubQuestion firstSubQ = data.getSubQuestions().get(0);
                            if (firstSubQ != null && firstSubQ.getOptions() != null && !firstSubQ.getOptions().isEmpty()) {
                                int detectedScaleLevel = firstSubQ.getOptions().size();
                                questionScaleLevelsCache.put(data.getNumId(), detectedScaleLevel);
                                questionScaleLevelsMap.put(data.getNumId(), detectedScaleLevel);
                            }
                        }
                    }
                }
                log.info("[分维度量表调整] 获取到的题目量表级数信息: {}", questionScaleLevelsCache);
                log.info("[分维度量表调整] 获取到的题目类型信息: {}", questionTypesCache);
                log.info("[分维度量表调整] 列编号到题目编号映射: {}", columnToQuestionMap);
            } catch (Exception e) {
                log.warn("[分维度量表调整] 获取题目结构信息失败，将使用默认验证范围: {}", e.getMessage());
            }

            // 将列编号转换为题目编号进行验证
            Set<Integer> actualQuestions = new HashSet<>();
            for (Integer columnNum : allQuestions) {
                Integer questionNum = columnToQuestionMap.get(columnNum);
                if (questionNum != null) {
                    actualQuestions.add(questionNum);
                } else {
                    log.warn("[分维度量表调整] 列编号{}没有对应的题目，跳过验证", columnNum);
                }
            }

            // 验证题目类型 - 分维度调整只支持特定题型
            validateQuestionTypesForMultiDimensional(actualQuestions, questionTypesCache);

            // 验证量表级数一致性
            validateScaleConsistency(actualQuestions, questionScaleLevelsMap);

            // 验证题目得分均值参数
            if (targetItemMeans != null && !targetItemMeans.isEmpty()) {
                if (targetItemMeans.size() != dimensions.size()) {
                    throw new IllegalArgumentException("题目得分均值列表数量必须与维度数量一致");
                }
                for (int i = 0; i < dimensions.size(); i++) {
                    if (targetItemMeans.get(i).size() != dimensions.get(i).size()) {
                        throw new IllegalArgumentException(String.format("维度%d的题目得分均值数量必须与该维度题目数量一致", i + 1));
                    }
                    // 验证每个题目的目标均值 - 根据实际量表级数动态验证
                    for (int j = 0; j < targetItemMeans.get(i).size(); j++) {
                        Double mean = targetItemMeans.get(i).get(j);
                        if (mean != null) {
                            Integer columnNum = dimensions.get(i).get(j);
                            // 将列编号转换为题目编号
                            Integer questionNum = columnToQuestionMap.get(columnNum);
                            Integer cachedScaleLevel = null;
                            if (questionNum != null) {
                                cachedScaleLevel = questionScaleLevelsCache.get(questionNum);
                            }

                            // 如果无法获取量表级数，使用传入的参数
                            int maxLevel = (cachedScaleLevel != null) ? cachedScaleLevel : scaleLevel;

                            if (mean < 1 || mean > maxLevel) {
                                throw new IllegalArgumentException(String.format("维度%d题目%d的目标均值应在1到%d之间（%d级量表），当前值：%.2f",
                                    i + 1, j + 1, maxLevel, maxLevel, mean));
                            }
                        }
                    }
                }
                log.info("[分维度量表调整] 使用指定的题目得分均值: {}", targetItemMeans);
            }

            // 验证得分方向参数
            if (scoringDirections != null && !scoringDirections.isEmpty()) {
                if (scoringDirections.size() != dimensions.size()) {
                    throw new IllegalArgumentException("得分方向列表数量必须与维度数量一致");
                }
                for (int i = 0; i < dimensions.size(); i++) {
                    if (scoringDirections.get(i).size() != dimensions.get(i).size()) {
                        throw new IllegalArgumentException(String.format("维度%d的得分方向数量必须与该维度题目数量一致", i + 1));
                    }
                    // 验证得分方向值
                    for (String direction : scoringDirections.get(i)) {
                        if (!"positive".equals(direction) && !"negative".equals(direction)) {
                            throw new IllegalArgumentException("得分方向只能是'positive'或'negative'");
                        }
                    }
                }
                log.info("[分维度量表调整] 使用指定的得分方向: {}", scoringDirections);
            } else {
                // 设置默认得分方向（全部为正向）
                scoringDirections = new ArrayList<>();
                for (List<Integer> dimension : dimensions) {
                    List<String> dimensionDirections = new ArrayList<>();
                    for (int j = 0; j < dimension.size(); j++) {
                        dimensionDirections.add("positive");
                    }
                    scoringDirections.add(dimensionDirections);
                }
                log.info("[分维度量表调整] 使用默认得分方向（全部正向）");
            }

            // ========== 第二阶段：数据预处理（反向题转正向题） ==========

            // 获取所有题目的列号
            List<Integer> allColumns = new ArrayList<>();
            for (List<Integer> dimension : dimensions) {
                allColumns.addAll(dimension);
            }

            // 获取原始数据
            List<List<Double>> originalData = new ArrayList<>();
            for (Integer col : allColumns) {
                List<Double> data = baseTools.getNumericColumnData(sessionId, col - 1);
                if (data.isEmpty()) {
                    throw new RuntimeException("第" + col + "列没有有效的数值数据");
                }
                originalData.add(new ArrayList<>(data));
            }

            // 记录哪些题目是反向计分，用于最后恢复
            Map<Integer, Boolean> reverseItemMap = new HashMap<>();

            // 将反向计分题转换为正向计分（统一处理）
            List<List<Double>> processedData = new ArrayList<>();
            for (int i = 0; i < originalData.size(); i++) {
                processedData.add(new ArrayList<>(originalData.get(i)));
            }

            for (int dimIdx = 0; dimIdx < dimensions.size(); dimIdx++) {
                List<Integer> dimension = dimensions.get(dimIdx);
                List<String> dimDirections = scoringDirections.get(dimIdx);

                for (int itemIdx = 0; itemIdx < dimension.size(); itemIdx++) {
                    Integer col = dimension.get(itemIdx);
                    String direction = dimDirections.get(itemIdx);

                    if ("negative".equals(direction)) {
                        reverseItemMap.put(col, true);
                        int dataIdx = getDataIndex(col, allColumns);

                        // 根据题目的实际量表级数进行反向转换
                        List<Double> originalCol = processedData.get(dataIdx);
                        List<Double> reversedCol = new ArrayList<>();

                        // 获取该题目的量表级数
                        Integer questionNum = columnToQuestionMap.get(col);
                        Integer cachedScaleLevel = null;
                        if (questionNum != null) {
                            cachedScaleLevel = questionScaleLevelsCache.get(questionNum);
                        }
                        int actualScaleLevel = (cachedScaleLevel != null) ? cachedScaleLevel : scaleLevel;

                        for (Double value : originalCol) {
                            if (value != null) {
                                // 动态计算反向转换公式：reversedValue = (actualScaleLevel + 1) - value
                                // 例如：5级量表(1-5)：6-1=5, 6-2=4, 6-3=3, 6-4=2, 6-5=1
                                // 例如：7级量表(1-7)：8-1=7, 8-2=6, 8-3=5, 8-4=4, 8-5=3, 8-6=2, 8-7=1
                                double reversedValue = (actualScaleLevel + 1) - value;
                                reversedCol.add(reversedValue);
                            } else {
                                reversedCol.add(null);
                            }
                        }
                        processedData.set(dataIdx, reversedCol);
                        log.info("[分维度量表调整] 题目{}从反向计分转换为正向计分进行统一处理（{}级量表）", col, actualScaleLevel);
                    }
                }
            }

            // 计算调整前的各维度信度（基于转换后的数据）
            List<Double> originalDimensionAlphas = new ArrayList<>();
            for (int dimIdx = 0; dimIdx < dimensions.size(); dimIdx++) {
                List<Integer> dimension = dimensions.get(dimIdx);
                List<List<Double>> dimensionData = new ArrayList<>();
                for (Integer col : dimension) {
                    int dataIdx = getDataIndex(col, allColumns);
                    dimensionData.add(processedData.get(dataIdx));
                }
                double alpha = calculateCronbachAlpha(dimensionData);
                originalDimensionAlphas.add(alpha);
                log.info("[分维度量表调整] 维度{}调整前信度: {}", dimension, alpha);
            }

            // 在计算信度和KMO之前，确保数据具有足够的变异性
            log.info("[分维度量表调整] 检查并修复数据变异性问题");
            if (!isDataValid(processedData)) {
                log.warn("[分维度量表调整] 检测到数据变异性问题，进行修复");
                processedData = ensureDataVariability(processedData);
                log.info("[分维度量表调整] 数据变异性修复完成");
            }

            // 计算调整前的总量表信度（基于转换后的数据）
            double originalTotalAlpha = calculateCronbachAlpha(processedData);
            log.info("[分维度量表调整] 总量表调整前信度: {}", originalTotalAlpha);

            // 计算调整前的KMO值（基于转换后的数据）
            double originalKMO = calculateKMOFromAdjustedData(processedData);
            log.info("[分维度量表调整] 调整前KMO值: {}", originalKMO);

            // ========== 第三阶段：数据调整（所有题目都按正向处理） ==========

            // 执行多维度调整（使用转换后的数据，所有题目都按正向处理）
            List<List<Double>> adjustedData = performMultiDimensionalAdjustmentUnified(
                processedData, dimensions, targetDimensionAlphas, targetTotalAlpha,
                targetKMO, targetInterDimensionCorrelation, tolerance, targetItemMeans, scaleLevel);

            // ========== 第四阶段：数据后处理和摘要生成 ==========

            // 保存用于摘要计算的数据（调整后但未反向转换的数据）
            List<List<Double>> summaryData = new ArrayList<>();
            for (List<Double> col : adjustedData) {
                summaryData.add(new ArrayList<>(col));
            }

            // 将反向计分题转换回原来的计分方式（用于存储到数据库）
            List<List<Double>> storageData = new ArrayList<>();
            for (List<Double> col : adjustedData) {
                storageData.add(new ArrayList<>(col));
            }

            if (!reverseItemMap.isEmpty()) {
                for (Map.Entry<Integer, Boolean> entry : reverseItemMap.entrySet()) {
                    Integer col = entry.getKey();
                    int dataIdx = getDataIndex(col, allColumns);

                    // 获取该题目的量表级数
                    Integer questionNum = columnToQuestionMap.get(col);
                    Integer cachedScaleLevel = null;
                    if (questionNum != null) {
                        cachedScaleLevel = questionScaleLevelsCache.get(questionNum);
                    }
                    int actualScaleLevel = (cachedScaleLevel != null) ? cachedScaleLevel : scaleLevel;

                    // 将正向计分转换回反向计分
                    List<Double> positiveCol = storageData.get(dataIdx);
                    List<Double> reversedCol = new ArrayList<>();
                    for (Double value : positiveCol) {
                        if (value != null) {
                            // 动态计算反向转换公式：reversedValue = (actualScaleLevel + 1) - value
                            // 例如：5级量表(1-5)：6-5=1, 6-4=2, 6-3=3, 6-2=4, 6-1=5
                            // 例如：7级量表(1-7)：8-7=1, 8-6=2, 8-5=3, 8-4=4, 8-3=5, 8-2=6, 8-1=7
                            double reversedValue = (actualScaleLevel + 1) - value;
                            reversedCol.add(reversedValue);
                        } else {
                            reversedCol.add(null);
                        }
                    }
                    storageData.set(dataIdx, reversedCol);
                    log.info("[分维度量表调整] 题目{}从正向计分转换回反向计分用于存储（{}级量表）", col, actualScaleLevel);
                }
            }

            // 生成变更记录（基于原始数据和存储数据的对比）
            List<List<Object>> changedCells = generateChangedCells(originalData, storageData, allColumns);

            // 先使用预估的调整后指标构建metrics，实际值会在saveAdjustedDataToMessage中重新计算
            List<Double> adjustedDimensionAlphas = new ArrayList<>(targetDimensionAlphas);
            double adjustedTotalAlpha = targetTotalAlpha != null ? targetTotalAlpha : originalTotalAlpha;
            double adjustedKMO = targetKMO != null ? targetKMO : originalKMO;

            // 构建包含调整前后数据的指标对象
            Map<String, Object> achievedMetrics = new HashMap<>();
            achievedMetrics.put("originalDimensionAlphas", originalDimensionAlphas);
            achievedMetrics.put("adjustedDimensionAlphas", adjustedDimensionAlphas);
            achievedMetrics.put("targetDimensionAlphas", targetDimensionAlphas);
            achievedMetrics.put("originalTotalAlpha", originalTotalAlpha);
            achievedMetrics.put("adjustedTotalAlpha", adjustedTotalAlpha);
            achievedMetrics.put("targetTotalAlpha", targetTotalAlpha);
            achievedMetrics.put("originalKMO", originalKMO);
            achievedMetrics.put("adjustedKMO", adjustedKMO);
            achievedMetrics.put("targetKMO", targetKMO);
            achievedMetrics.put("dimensions", dimensions);
            achievedMetrics.put("allColumns", allColumns);
            achievedMetrics.put("cellsChanged", changedCells.size());

            // 生成调整说明
            String explanation = generateMultiDimensionalExplanation(
                dimensions, originalDimensionAlphas, adjustedDimensionAlphas, targetDimensionAlphas,
                originalTotalAlpha, adjustedTotalAlpha, targetTotalAlpha,
                originalKMO, adjustedKMO, targetKMO,
                changedCells.size(), achievedMetrics);

            // 构建包含文字描述数据的表格摘要（使用调整后但未反向转换的内存数据）
            Map<String, Object> summaryMetrics = buildSummaryMetrics(
                dimensions, originalDimensionAlphas, adjustedDimensionAlphas, targetDimensionAlphas,
                originalTotalAlpha, adjustedTotalAlpha, targetTotalAlpha,
                originalKMO, adjustedKMO, targetKMO, changedCells.size(), targetItemMeans, scoringDirections,
                summaryData, allColumns);

            // 将详细表格数据添加到metrics中，让baseTools处理
            summaryMetrics.put("isMultiDimensionalAdjustment", true);
            summaryMetrics.put("detailedTableData", summaryMetrics.get("summaryTableData"));
            summaryMetrics.put("detailedTableHeaders", summaryMetrics.get("summaryTableHeaders"));

            // 添加必要信息让saveAdjustedDataToMessage能够重新计算实际指标
            summaryMetrics.put("dimensions", dimensions);
            summaryMetrics.put("allColumns", allColumns);
            summaryMetrics.put("targetDimensionAlphas", targetDimensionAlphas);
            summaryMetrics.put("targetTotalAlpha", targetTotalAlpha);
            summaryMetrics.put("targetKMO", targetKMO);
            summaryMetrics.put("targetItemMeans", targetItemMeans);
            summaryMetrics.put("scoringDirections", scoringDirections);
            summaryMetrics.put("originalDimensionAlphas", originalDimensionAlphas);
            summaryMetrics.put("originalTotalAlpha", originalTotalAlpha);
            summaryMetrics.put("originalKMO", originalKMO);

            // 生成配置文本JSON
            String configText = generateConfigText(dimensions, scaleLevel, targetDimensionAlphas, targetTotalAlpha,
                targetKMO, targetInterDimensionCorrelation, tolerance, targetItemMeans, scoringDirections,
                columnToQuestionMap, questionDataMap);

            // 将配置文本添加到metrics中
            summaryMetrics.put("configText", configText);

            // 最终保护：在保存数据之前进行最后的变异性检查，确保KMO不会为NaN
            log.info("[最终保护] 保存数据前进行最后的变异性检查");
            boolean needsFinalFix = false;

            // 检查每列的变异性
            for (Integer col : allColumns) {
                List<Double> columnData = baseTools.getNumericColumnData(sessionId, col - 1);
                if (columnData != null && !columnData.isEmpty()) {
                    DescriptiveStatistics stats = new DescriptiveStatistics();
                    columnData.forEach(stats::addValue);

                    if (stats.getVariance() <= 1e-3 || stats.getStandardDeviation() <= 0.05) {
                        log.warn("[最终保护] 第{}列变异性不足，需要修复", col);
                        needsFinalFix = true;
                        break;
                    }
                }
            }

            if (needsFinalFix) {
                log.warn("[最终保护] 检测到变异性问题，进行最后修复");
                // 重新确保所有数据的变异性
                List<List<Double>> finalData = new ArrayList<>();
                for (Integer col : allColumns) {
                    List<Double> data = baseTools.getNumericColumnData(sessionId, col - 1);
                    finalData.add(data);
                }
                finalData = ensureDataVariability(finalData);

                // 重新生成变更记录
                List<List<Object>> finalChangedCells = generateChangedCells(originalData, finalData, allColumns);

                log.info("[最终保护] 修复完成，重新保存数据");
                return baseTools.saveAdjustedDataToMessage(sessionId, finalChangedCells, explanation, summaryMetrics);
            }

            // 保存调整后的数据到数据库，并返回分析结果给AI
            return baseTools.saveAdjustedDataToMessage(sessionId, changedCells, explanation, summaryMetrics);

        } catch (Exception e) {
            log.error("[分维度量表调整] 调整失败", e);
            return "分维度量表调整失败: " + e.getMessage();
        }
    }

    /**
     * 执行多维度调整的核心算法 - 全新分层优化版本
     * 采用分层（分维度）+ 多阶段优化（随机搜索+局部优化+智能交换+模拟退火）
     * 专门针对因子分析的维度聚集性进行优化
     */
    private List<List<Double>> performMultiDimensionalAdjustmentUnified(
            List<List<Double>> processedData, List<List<Integer>> dimensions,
            List<Double> targetDimensionAlphas, Double targetTotalAlpha,
            Double targetKMO, Double targetInterDimensionCorrelation, Double tolerance,
            List<List<Double>> targetItemMeans, Integer scaleLevel) {

        log.info("[分层优化] 开始执行全新分层多维度优化算法，专注因子结构聚集性");

        List<List<Double>> bestData = new ArrayList<>();
        for (List<Double> col : processedData) {
            bestData.add(new ArrayList<>(col));
        }

        // 第一阶段：分层预处理 - 按维度分别处理
        log.info("[分层优化] 第一阶段：分层预处理，按维度分别优化");
        bestData = performLayeredPreprocessing(bestData, dimensions, targetDimensionAlphas, targetItemMeans, scaleLevel);

        // 第二阶段：多阶段优化 - 针对因子结构
        log.info("[分层优化] 第二阶段：多阶段优化，专注因子结构聚集性");
        bestData = performMultiStageFactorOptimization(bestData, dimensions, targetDimensionAlphas,
                                                      targetTotalAlpha, targetKMO, targetInterDimensionCorrelation,
                                                      tolerance, scaleLevel);

        // 第三阶段：全局协调优化
        log.info("[分层优化] 第三阶段：全局协调优化");
        bestData = performGlobalCoordinationOptimization(bestData, dimensions, targetDimensionAlphas,
                                                        targetTotalAlpha, targetKMO, targetItemMeans,
                                                        tolerance, scaleLevel);

        return bestData;
    }

    /**
     * 第一阶段：分层预处理 - 按维度分别处理
     */
    private List<List<Double>> performLayeredPreprocessing(List<List<Double>> data,
                                                          List<List<Integer>> dimensions,
                                                          List<Double> targetDimensionAlphas,
                                                          List<List<Double>> targetItemMeans,
                                                          Integer scaleLevel) {

        log.info("[分层预处理] 开始按维度分层处理，共{}个维度", dimensions.size());

        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        List<Integer> allColumns = getAllColumns(dimensions);

        // 为每个维度单独优化
        for (int dimIdx = 0; dimIdx < dimensions.size(); dimIdx++) {
            List<Integer> dimension = dimensions.get(dimIdx);
            double targetAlpha = targetDimensionAlphas.get(dimIdx);

            log.info("[分层预处理] 处理维度{}，包含题目{}，目标信度{:.3f}",
                    dimIdx + 1, dimension, targetAlpha);

            // 提取当前维度的数据
            List<List<Double>> dimensionData = new ArrayList<>();
            List<Integer> dimensionDataIndices = new ArrayList<>();
            for (Integer col : dimension) {
                int dataIdx = getDataIndex(col, allColumns);
                dimensionData.add(new ArrayList<>(result.get(dataIdx)));
                dimensionDataIndices.add(dataIdx);
            }

            // 单维度优化：增强内部相关性
            dimensionData = optimizeSingleDimensionCorrelations(dimensionData, targetAlpha, scaleLevel);

            // 如果指定了均值目标，调整均值
            if (targetItemMeans != null && dimIdx < targetItemMeans.size()) {
                List<Double> dimensionTargetMeans = targetItemMeans.get(dimIdx);
                if (dimensionTargetMeans != null && !dimensionTargetMeans.isEmpty()) {
                    dimensionData = adjustDimensionItemMeans(dimensionData, dimensionTargetMeans, scaleLevel);
                }
            }

            // 将优化后的数据放回原位置
            for (int i = 0; i < dimension.size(); i++) {
                result.set(dimensionDataIndices.get(i), dimensionData.get(i));
            }
        }

        log.info("[分层预处理] 分层预处理完成");
        return result;
    }

    /**
     * 优化单个维度内部的相关性
     */
    private List<List<Double>> optimizeSingleDimensionCorrelations(List<List<Double>> dimensionData,
                                                                  double targetAlpha,
                                                                  Integer scaleLevel) {

        log.debug("[单维度优化] 开始优化维度内部相关性，目标信度: {:.3f}", targetAlpha);

        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : dimensionData) {
            result.add(new ArrayList<>(col));
        }

        int maxIterations = 20;
        double tolerance = 0.02;

        for (int iter = 0; iter < maxIterations; iter++) {
            double currentAlpha = calculateCronbachAlpha(result);
            double alphaGap = targetAlpha - currentAlpha;

            if (Math.abs(alphaGap) <= tolerance) {
                log.debug("[单维度优化] 第{}次迭代达到目标信度: {:.3f}", iter, currentAlpha);
                break;
            }

            if (alphaGap > 0) {
                // 需要提高信度：增强相关性
                double enhancementStrength = Math.min(0.3, Math.abs(alphaGap) * 1.5);
                result = enhanceIntraDimensionCorrelations(result, enhancementStrength, scaleLevel);
            } else {
                // 需要降低信度：增加差异性
                double diversityStrength = Math.min(0.2, Math.abs(alphaGap) * 1.0);
                result = addControlledDiversity(result, diversityStrength, scaleLevel);
            }
        }

        return result;
    }

    /**
     * 增强维度内部相关性
     */
    private List<List<Double>> enhanceIntraDimensionCorrelations(List<List<Double>> data,
                                                               double strength,
                                                               Integer scaleLevel) {

        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        int numItems = result.size();
        int sampleSize = result.get(0).size();

        // 计算共同因子（所有题目的平均值）
        List<Double> commonFactor = new ArrayList<>();
        for (int i = 0; i < sampleSize; i++) {
            double sum = 0.0;
            for (List<Double> item : result) {
                sum += item.get(i);
            }
            commonFactor.add(sum / numItems);
        }

        // 让每个题目向共同因子靠拢
        for (int itemIdx = 0; itemIdx < numItems; itemIdx++) {
            List<Double> item = result.get(itemIdx);
            for (int i = 0; i < sampleSize; i++) {
                if (Math.random() < 0.4) { // 40%的数据点进行调整
                    double originalValue = item.get(i);
                    double commonValue = commonFactor.get(i);

                    // 向共同因子方向调整
                    double adjustment = (commonValue - originalValue) * strength;
                    double newValue = originalValue + adjustment;

                    // 限制在量表范围内
                    newValue = Math.max(1.0, Math.min((double)scaleLevel, newValue));
                    item.set(i, newValue);
                }
            }
        }

        return result;
    }

    /**
     * 添加受控的差异性
     */
    private List<List<Double>> addControlledDiversity(List<List<Double>> data,
                                                     double strength,
                                                     Integer scaleLevel) {

        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        Random random = new Random(42);

        // 为每个题目添加独特的变异
        for (int itemIdx = 0; itemIdx < result.size(); itemIdx++) {
            List<Double> item = result.get(itemIdx);

            // 计算题目特有的变异模式
            double itemVariation = (itemIdx + 1) * strength;

            for (int i = 0; i < item.size(); i++) {
                if (Math.random() < 0.3) { // 30%的数据点进行调整
                    double originalValue = item.get(i);
                    double noise = random.nextGaussian() * itemVariation;
                    double newValue = originalValue + noise;

                    // 限制在量表范围内
                    newValue = Math.max(1.0, Math.min((double)scaleLevel, newValue));
                    item.set(i, newValue);
                }
            }
        }

        return result;
    }

    /**
     * 调整维度内题目均值
     */
    private List<List<Double>> adjustDimensionItemMeans(List<List<Double>> dimensionData,
                                                       List<Double> targetMeans,
                                                       Integer scaleLevel) {

        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : dimensionData) {
            result.add(new ArrayList<>(col));
        }

        for (int i = 0; i < Math.min(result.size(), targetMeans.size()); i++) {
            if (targetMeans.get(i) != null) {
                result.set(i, adjustSingleItemMean(result.get(i), targetMeans.get(i), scaleLevel));
            }
        }

        return result;
    }

    /**
     * 第二阶段：多阶段因子优化 - 专注因子结构聚集性
     */
    private List<List<Double>> performMultiStageFactorOptimization(List<List<Double>> data,
                                                                  List<List<Integer>> dimensions,
                                                                  List<Double> targetDimensionAlphas,
                                                                  Double targetTotalAlpha,
                                                                  Double targetKMO,
                                                                  Double targetInterDimensionCorrelation,
                                                                  Double tolerance,
                                                                  Integer scaleLevel) {

        log.info("[多阶段因子优化] 开始多阶段因子结构优化");

        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        // 阶段1：随机搜索 - 探索可能的解空间
        log.info("[多阶段因子优化] 阶段1：随机搜索优化");
        result = performRandomSearchOptimization(result, dimensions, scaleLevel);

        // 阶段2：局部优化 - 基于梯度的精细调整
        log.info("[多阶段因子优化] 阶段2：局部优化");
        result = performLocalOptimization(result, dimensions, targetDimensionAlphas, targetTotalAlpha,
                                        targetKMO, tolerance, scaleLevel);

        // 阶段3：智能交换 - 基于因子载荷的智能数据交换
        log.info("[多阶段因子优化] 阶段3：智能交换优化");
        result = performIntelligentSwapOptimization(result, dimensions, scaleLevel);

        // 阶段4：模拟退火 - 全局优化避免局部最优
        log.info("[多阶段因子优化] 阶段4：模拟退火优化");
        result = performSimulatedAnnealingOptimization(result, dimensions, targetDimensionAlphas,
                                                     targetTotalAlpha, targetKMO, scaleLevel);

        return result;
    }

    /**
     * 阶段1：随机搜索优化
     */
    private List<List<Double>> performRandomSearchOptimization(List<List<Double>> data,
                                                              List<List<Integer>> dimensions,
                                                              Integer scaleLevel) {

        log.debug("[随机搜索] 开始随机搜索优化");

        List<List<Double>> bestResult = new ArrayList<>();
        for (List<Double> col : data) {
            bestResult.add(new ArrayList<>(col));
        }

        double bestFactorScore = evaluateFactorStructureScore(bestResult, dimensions);

        int numTrials = 50; // 随机试验次数
        Random random = new Random(42);

        for (int trial = 0; trial < numTrials; trial++) {
            List<List<Double>> candidate = new ArrayList<>();
            for (List<Double> col : data) {
                candidate.add(new ArrayList<>(col));
            }

            // 随机调整部分数据点
            for (List<Double> item : candidate) {
                for (int i = 0; i < item.size(); i++) {
                    if (random.nextDouble() < 0.1) { // 10%的数据点进行随机调整
                        double noise = random.nextGaussian() * 0.3;
                        double newValue = item.get(i) + noise;
                        newValue = Math.max(1.0, Math.min((double)scaleLevel, newValue));
                        item.set(i, newValue);
                    }
                }
            }

            double candidateScore = evaluateFactorStructureScore(candidate, dimensions);
            if (candidateScore > bestFactorScore) {
                bestFactorScore = candidateScore;
                bestResult = candidate;
                log.debug("[随机搜索] 第{}次试验找到更好解，因子得分: {:.4f}", trial, candidateScore);
            }
        }

        log.debug("[随机搜索] 随机搜索完成，最佳因子得分: {:.4f}", bestFactorScore);
        return bestResult;
    }

    /**
     * 评估因子结构得分 - 衡量维度聚集性
     */
    private double evaluateFactorStructureScore(List<List<Double>> data, List<List<Integer>> dimensions) {
        try {
            // 执行简化的因子分析来评估维度聚集性
            List<Integer> allColumns = getAllColumns(dimensions);

            // 计算相关矩阵
            double[][] correlationMatrix = calculateCorrelationMatrix(data);

            // 计算维度内平均相关性
            double intraDimensionCorrelation = 0.0;
            int intraPairs = 0;

            for (List<Integer> dimension : dimensions) {
                for (int i = 0; i < dimension.size() - 1; i++) {
                    for (int j = i + 1; j < dimension.size(); j++) {
                        int idx1 = getDataIndex(dimension.get(i), allColumns);
                        int idx2 = getDataIndex(dimension.get(j), allColumns);
                        intraDimensionCorrelation += Math.abs(correlationMatrix[idx1][idx2]);
                        intraPairs++;
                    }
                }
            }
            if (intraPairs > 0) {
                intraDimensionCorrelation /= intraPairs;
            }

            // 计算维度间平均相关性
            double interDimensionCorrelation = 0.0;
            int interPairs = 0;

            for (int dim1 = 0; dim1 < dimensions.size() - 1; dim1++) {
                for (int dim2 = dim1 + 1; dim2 < dimensions.size(); dim2++) {
                    List<Integer> dimension1 = dimensions.get(dim1);
                    List<Integer> dimension2 = dimensions.get(dim2);

                    for (Integer col1 : dimension1) {
                        for (Integer col2 : dimension2) {
                            int idx1 = getDataIndex(col1, allColumns);
                            int idx2 = getDataIndex(col2, allColumns);
                            interDimensionCorrelation += Math.abs(correlationMatrix[idx1][idx2]);
                            interPairs++;
                        }
                    }
                }
            }
            if (interPairs > 0) {
                interDimensionCorrelation /= interPairs;
            }

            // 因子结构得分 = 维度内相关性 - 维度间相关性
            // 好的因子结构应该有高的维度内相关性和低的维度间相关性
            double factorScore = intraDimensionCorrelation - interDimensionCorrelation;

            return factorScore;

        } catch (Exception e) {
            log.warn("[因子结构评估] 评估失败: {}", e.getMessage());
            return 0.0;
        }
    }

    /**
     * 计算相关矩阵
     */
    private double[][] calculateCorrelationMatrix(List<List<Double>> data) {
        int numVars = data.size();
        double[][] correlationMatrix = new double[numVars][numVars];

        for (int i = 0; i < numVars; i++) {
            for (int j = 0; j < numVars; j++) {
                if (i == j) {
                    correlationMatrix[i][j] = 1.0;
                } else {
                    correlationMatrix[i][j] = calculateCorrelation(data.get(i), data.get(j));
                }
            }
        }

        return correlationMatrix;
    }

    /**
     * 阶段2：局部优化
     */
    private List<List<Double>> performLocalOptimization(List<List<Double>> data,
                                                       List<List<Integer>> dimensions,
                                                       List<Double> targetDimensionAlphas,
                                                       Double targetTotalAlpha,
                                                       Double targetKMO,
                                                       Double tolerance,
                                                       Integer scaleLevel) {

        log.debug("[局部优化] 开始局部优化");

        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        List<Integer> allColumns = getAllColumns(dimensions);
        int maxIterations = 30;

        for (int iter = 0; iter < maxIterations; iter++) {
            boolean hasImprovement = false;

            // 针对每个维度进行局部优化
            for (int dimIdx = 0; dimIdx < dimensions.size(); dimIdx++) {
                List<Integer> dimension = dimensions.get(dimIdx);
                double targetAlpha = targetDimensionAlphas.get(dimIdx);

                // 提取维度数据
                List<List<Double>> dimensionData = new ArrayList<>();
                List<Integer> dimensionDataIndices = new ArrayList<>();
                for (Integer col : dimension) {
                    int dataIdx = getDataIndex(col, allColumns);
                    dimensionData.add(new ArrayList<>(result.get(dataIdx)));
                    dimensionDataIndices.add(dataIdx);
                }

                double currentAlpha = calculateCronbachAlpha(dimensionData);
                double alphaGap = targetAlpha - currentAlpha;

                if (Math.abs(alphaGap) > tolerance) {
                    // 执行梯度式调整
                    dimensionData = performGradientAdjustment(dimensionData, alphaGap, scaleLevel);

                    // 将调整后的数据放回
                    for (int i = 0; i < dimension.size(); i++) {
                        result.set(dimensionDataIndices.get(i), dimensionData.get(i));
                    }

                    hasImprovement = true;
                }
            }

            if (!hasImprovement) {
                log.debug("[局部优化] 第{}次迭代无改进，提前结束", iter);
                break;
            }
        }

        log.debug("[局部优化] 局部优化完成");
        return result;
    }

    /**
     * 执行梯度式调整
     */
    private List<List<Double>> performGradientAdjustment(List<List<Double>> dimensionData,
                                                        double alphaGap,
                                                        Integer scaleLevel) {

        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : dimensionData) {
            result.add(new ArrayList<>(col));
        }

        double adjustmentStrength = Math.min(0.2, Math.abs(alphaGap) * 0.8);

        if (alphaGap > 0) {
            // 需要提高信度：增强相关性
            result = enhanceIntraDimensionCorrelations(result, adjustmentStrength, scaleLevel);
        } else {
            // 需要降低信度：增加差异性
            result = addControlledDiversity(result, adjustmentStrength, scaleLevel);
        }

        return result;
    }

    /**
     * 阶段3：智能交换优化
     */
    private List<List<Double>> performIntelligentSwapOptimization(List<List<Double>> data,
                                                                List<List<Integer>> dimensions,
                                                                Integer scaleLevel) {

        log.debug("[智能交换] 开始智能交换优化");

        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        double bestFactorScore = evaluateFactorStructureScore(result, dimensions);
        int numSwaps = 100; // 交换次数
        Random random = new Random(42);

        for (int swap = 0; swap < numSwaps; swap++) {
            // 随机选择两个不同维度的题目进行数据交换
            if (dimensions.size() < 2) continue;

            int dim1 = random.nextInt(dimensions.size());
            int dim2 = random.nextInt(dimensions.size());
            while (dim2 == dim1) {
                dim2 = random.nextInt(dimensions.size());
            }

            List<Integer> dimension1 = dimensions.get(dim1);
            List<Integer> dimension2 = dimensions.get(dim2);

            if (dimension1.isEmpty() || dimension2.isEmpty()) continue;

            int item1Idx = random.nextInt(dimension1.size());
            int item2Idx = random.nextInt(dimension2.size());

            List<Integer> allColumns = getAllColumns(dimensions);
            int dataIdx1 = getDataIndex(dimension1.get(item1Idx), allColumns);
            int dataIdx2 = getDataIndex(dimension2.get(item2Idx), allColumns);

            // 随机选择几个数据点进行交换
            List<Double> item1 = result.get(dataIdx1);
            List<Double> item2 = result.get(dataIdx2);

            List<Integer> swapIndices = new ArrayList<>();
            for (int i = 0; i < Math.min(5, item1.size()); i++) {
                swapIndices.add(random.nextInt(item1.size()));
            }

            // 执行交换
            for (Integer idx : swapIndices) {
                double temp = item1.get(idx);
                item1.set(idx, item2.get(idx));
                item2.set(idx, temp);
            }

            // 评估交换后的效果
            double newFactorScore = evaluateFactorStructureScore(result, dimensions);
            if (newFactorScore <= bestFactorScore) {
                // 如果没有改进，撤销交换
                for (Integer idx : swapIndices) {
                    double temp = item1.get(idx);
                    item1.set(idx, item2.get(idx));
                    item2.set(idx, temp);
                }
            } else {
                bestFactorScore = newFactorScore;
                log.debug("[智能交换] 第{}次交换改进了因子得分: {:.4f}", swap, newFactorScore);
            }
        }

        log.debug("[智能交换] 智能交换优化完成，最终因子得分: {:.4f}", bestFactorScore);
        return result;
    }

    /**
     * 阶段4：模拟退火优化
     */
    private List<List<Double>> performSimulatedAnnealingOptimization(List<List<Double>> data,
                                                                   List<List<Integer>> dimensions,
                                                                   List<Double> targetDimensionAlphas,
                                                                   Double targetTotalAlpha,
                                                                   Double targetKMO,
                                                                   Integer scaleLevel) {

        log.debug("[模拟退火] 开始模拟退火优化");

        List<List<Double>> currentSolution = new ArrayList<>();
        for (List<Double> col : data) {
            currentSolution.add(new ArrayList<>(col));
        }

        List<List<Double>> bestSolution = new ArrayList<>();
        for (List<Double> col : currentSolution) {
            bestSolution.add(new ArrayList<>(col));
        }

        double currentScore = evaluateComprehensiveScore(currentSolution, dimensions, targetDimensionAlphas,
                                                       targetTotalAlpha, targetKMO);
        double bestScore = currentScore;

        // 模拟退火参数
        double initialTemperature = 1.0;
        double finalTemperature = 0.01;
        int maxIterations = 200;
        double coolingRate = Math.pow(finalTemperature / initialTemperature, 1.0 / maxIterations);

        Random random = new Random(42);

        for (int iter = 0; iter < maxIterations; iter++) {
            double temperature = initialTemperature * Math.pow(coolingRate, iter);

            // 生成邻域解
            List<List<Double>> neighborSolution = generateNeighborSolution(currentSolution, scaleLevel, random);
            double neighborScore = evaluateComprehensiveScore(neighborSolution, dimensions, targetDimensionAlphas,
                                                            targetTotalAlpha, targetKMO);

            // 决定是否接受新解
            boolean accept = false;
            if (neighborScore > currentScore) {
                accept = true; // 更好的解直接接受
            } else {
                // 较差的解按概率接受
                double probability = Math.exp((neighborScore - currentScore) / temperature);
                accept = random.nextDouble() < probability;
            }

            if (accept) {
                currentSolution = neighborSolution;
                currentScore = neighborScore;

                if (currentScore > bestScore) {
                    bestScore = currentScore;
                    bestSolution = new ArrayList<>();
                    for (List<Double> col : currentSolution) {
                        bestSolution.add(new ArrayList<>(col));
                    }
                    log.debug("[模拟退火] 第{}次迭代找到更好解，得分: {:.4f}", iter, bestScore);
                }
            }
        }

        log.debug("[模拟退火] 模拟退火优化完成，最佳得分: {:.4f}", bestScore);
        return bestSolution;
    }

    /**
     * 评估综合得分（包含信度、KMO和因子结构）
     */
    private double evaluateComprehensiveScore(List<List<Double>> data,
                                            List<List<Integer>> dimensions,
                                            List<Double> targetDimensionAlphas,
                                            Double targetTotalAlpha,
                                            Double targetKMO) {

        double score = 0.0;
        List<Integer> allColumns = getAllColumns(dimensions);

        try {
            // 因子结构得分（权重35%）
            double factorScore = evaluateFactorStructureScore(data, dimensions);
            score += factorScore * 0.35;

            // 维度信度得分（权重25%）
            double alphaScore = 0.0;
            for (int dimIdx = 0; dimIdx < dimensions.size(); dimIdx++) {
                List<Integer> dimension = dimensions.get(dimIdx);
                double targetAlpha = targetDimensionAlphas.get(dimIdx);

                List<List<Double>> dimensionData = new ArrayList<>();
                for (Integer col : dimension) {
                    int dataIdx = getDataIndex(col, allColumns);
                    dimensionData.add(data.get(dataIdx));
                }

                double currentAlpha = calculateCronbachAlpha(dimensionData);
                double alphaDeviation = Math.abs(currentAlpha - targetAlpha);
                alphaScore += Math.max(0, 1.0 - alphaDeviation * 2.0); // 偏差越小得分越高
            }
            alphaScore /= dimensions.size();
            score += alphaScore * 0.25;

            // 总信度得分（权重25%，提高重要性）
            if (targetTotalAlpha != null) {
                double totalAlpha = calculateCronbachAlpha(data);
                double totalAlphaDeviation = Math.abs(totalAlpha - targetTotalAlpha);
                // 使用更严格的评分标准
                double totalAlphaScore = Math.max(0, 1.0 - totalAlphaDeviation * 3.0);
                score += totalAlphaScore * 0.25;
            }

            // KMO得分（权重15%）
            if (targetKMO != null) {
                double currentKMO = calculateKMOFromAdjustedData(data);
                if (!Double.isNaN(currentKMO)) {
                    double kmoDeviation = Math.abs(currentKMO - targetKMO);
                    double kmoScore = Math.max(0, 1.0 - kmoDeviation * 2.0);
                    score += kmoScore * 0.15;
                }
            }

        } catch (Exception e) {
            log.warn("[综合评估] 评估失败: {}", e.getMessage());
            return 0.0;
        }

        return score;
    }

    /**
     * 生成邻域解
     */
    private List<List<Double>> generateNeighborSolution(List<List<Double>> currentSolution,
                                                       Integer scaleLevel,
                                                       Random random) {

        List<List<Double>> neighbor = new ArrayList<>();
        for (List<Double> col : currentSolution) {
            neighbor.add(new ArrayList<>(col));
        }

        // 随机选择一个题目进行微调
        int itemIdx = random.nextInt(neighbor.size());
        List<Double> item = neighbor.get(itemIdx);

        // 随机选择几个数据点进行微调
        int numAdjustments = Math.min(5, item.size() / 10);
        for (int i = 0; i < numAdjustments; i++) {
            int dataIdx = random.nextInt(item.size());
            double currentValue = item.get(dataIdx);
            double noise = random.nextGaussian() * 0.2;
            double newValue = currentValue + noise;
            newValue = Math.max(1.0, Math.min((double)scaleLevel, newValue));
            item.set(dataIdx, newValue);
        }

        return neighbor;
    }

    /**
     * 第三阶段：全局协调优化
     */
    private List<List<Double>> performGlobalCoordinationOptimization(List<List<Double>> data,
                                                                    List<List<Integer>> dimensions,
                                                                    List<Double> targetDimensionAlphas,
                                                                    Double targetTotalAlpha,
                                                                    Double targetKMO,
                                                                    List<List<Double>> targetItemMeans,
                                                                    Double tolerance,
                                                                    Integer scaleLevel) {

        log.info("[全局协调] 开始全局协调优化");

        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        List<Integer> allColumns = getAllColumns(dimensions);
        int maxIterations = 20;

        for (int iter = 0; iter < maxIterations; iter++) {
            log.debug("[全局协调] 第{}次全局协调迭代", iter + 1);

            // 1. 协调维度信度
            result = coordinateDimensionReliabilities(result, dimensions, targetDimensionAlphas,
                                                    tolerance, allColumns, scaleLevel);

            // 2. 强化总信度协调（大幅增加频率和强度）
            if (targetTotalAlpha != null) {
                // 每次迭代都进行总信度协调
                result = coordinateTotalReliability(result, targetTotalAlpha, tolerance, scaleLevel);

                // 每次迭代都进行总信度强化
                result = enhanceTotalReliabilityFocus(result, targetTotalAlpha, tolerance, scaleLevel);

                // 每3次迭代进行一次深度总信度优化
                if (iter % 3 == 0) {
                    result = deepTotalReliabilityOptimization(result, targetTotalAlpha, tolerance, scaleLevel);
                }
            }

            // 3. 协调KMO值
            if (targetKMO != null) {
                result = coordinateKMO(result, targetKMO, tolerance, scaleLevel);
            }

            // 4. 协调题目均值（如果指定）
            if (targetItemMeans != null && !targetItemMeans.isEmpty()) {
                result = coordinateItemMeans(result, dimensions, targetItemMeans, allColumns, scaleLevel);
            }

            // 5. 最终因子结构微调
            result = performFinalFactorStructureTuning(result, dimensions, scaleLevel);

            // 检查收敛
            if (checkGlobalConvergence(result, dimensions, targetDimensionAlphas, targetTotalAlpha,
                                     targetKMO, tolerance)) {
                log.info("[全局协调] 第{}次迭代达到全局收敛", iter + 1);
                break;
            }
        }

        log.info("[全局协调] 全局协调优化完成");
        return result;
    }

    /**
     * 专门强化总信度的方法
     */
    private List<List<Double>> enhanceTotalReliabilityFocus(List<List<Double>> data,
                                                           Double targetTotalAlpha,
                                                           Double tolerance,
                                                           Integer scaleLevel) {

        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        double currentAlpha = calculateCronbachAlpha(result);
        double alphaGap = targetTotalAlpha - currentAlpha;

        log.debug("[总信度强化] 当前总信度: {:.4f}, 目标: {:.4f}, 差距: {:.4f}",
                 currentAlpha, targetTotalAlpha, alphaGap);

        if (Math.abs(alphaGap) > tolerance) {
            if (alphaGap > 0) {
                // 需要提高总信度：使用强化策略
                double strength = Math.min(0.15, Math.abs(alphaGap) * 0.6);

                // 策略1：增强全局一致性
                result = enhanceGlobalConsistencyForTotalAlpha(result, strength, scaleLevel);

                // 策略2：减少异常值影响
                result = reduceOutlierImpactForTotalAlpha(result, strength, scaleLevel);

            } else {
                // 需要降低总信度：适度增加差异
                double strength = Math.min(0.1, Math.abs(alphaGap) * 0.4);
                result = addControlledVariationForTotalAlpha(result, strength, scaleLevel);
            }
        }

        double finalAlpha = calculateCronbachAlpha(result);
        log.debug("[总信度强化] 强化完成: {:.4f} -> {:.4f}", currentAlpha, finalAlpha);

        return result;
    }

    /**
     * 为总信度增强全局一致性
     */
    private List<List<Double>> enhanceGlobalConsistencyForTotalAlpha(List<List<Double>> data,
                                                                    double strength,
                                                                    Integer scaleLevel) {

        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        int sampleSize = result.get(0).size();

        // 计算每个样本的标准化得分
        for (int sampleIdx = 0; sampleIdx < sampleSize; sampleIdx++) {
            if (Math.random() < strength * 2) { // 增加处理概率
                List<Double> sampleValues = new ArrayList<>();
                for (List<Double> item : result) {
                    sampleValues.add(item.get(sampleIdx));
                }

                // 计算样本的整体趋势
                double sampleMean = sampleValues.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
                double sampleStd = calculateStandardDeviation(sampleValues);

                // 让所有项目向一致的方向调整
                for (int itemIdx = 0; itemIdx < result.size(); itemIdx++) {
                    double currentValue = result.get(itemIdx).get(sampleIdx);
                    double zScore = sampleStd > 0 ? (currentValue - sampleMean) / sampleStd : 0;

                    // 减少极端偏差
                    if (Math.abs(zScore) > 1.0) {
                        double adjustment = -zScore * 0.3 * strength;
                        double newValue = currentValue + adjustment;
                        newValue = Math.max(1.0, Math.min((double)scaleLevel, newValue));
                        result.get(itemIdx).set(sampleIdx, newValue);
                    }
                }
            }
        }

        return result;
    }

    /**
     * 减少异常值对总信度的影响
     */
    private List<List<Double>> reduceOutlierImpactForTotalAlpha(List<List<Double>> data,
                                                               double strength,
                                                               Integer scaleLevel) {

        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        // 对每个项目进行异常值处理
        for (List<Double> item : result) {
            List<Double> values = new ArrayList<>(item);
            double mean = values.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
            double std = calculateStandardDeviation(values);

            if (std > 0) {
                for (int i = 0; i < item.size(); i++) {
                    double value = item.get(i);
                    double zScore = (value - mean) / std;

                    // 处理异常值
                    if (Math.abs(zScore) > 2.0 && Math.random() < strength) {
                        double adjustment = -zScore * 0.4 * strength;
                        double newValue = value + adjustment;
                        newValue = Math.max(1.0, Math.min((double)scaleLevel, newValue));
                        item.set(i, newValue);
                    }
                }
            }
        }

        return result;
    }

    /**
     * 为总信度添加受控变异
     */
    private List<List<Double>> addControlledVariationForTotalAlpha(List<List<Double>> data,
                                                                  double strength,
                                                                  Integer scaleLevel) {

        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        Random random = new Random(42);

        // 为部分项目添加适度的独特性
        for (int itemIdx = 0; itemIdx < result.size(); itemIdx++) {
            if (Math.random() < 0.4) { // 40%的项目参与
                List<Double> item = result.get(itemIdx);
                for (int i = 0; i < item.size(); i++) {
                    if (Math.random() < strength) {
                        double originalValue = item.get(i);
                        double noise = random.nextGaussian() * 0.3;
                        double newValue = originalValue + noise;

                        newValue = Math.max(1.0, Math.min((double)scaleLevel, newValue));
                        item.set(i, newValue);
                    }
                }
            }
        }

        return result;
    }

    /**
     * 深度总信度优化
     */
    private List<List<Double>> deepTotalReliabilityOptimization(List<List<Double>> data,
                                                               Double targetTotalAlpha,
                                                               Double tolerance,
                                                               Integer scaleLevel) {

        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        double currentAlpha = calculateCronbachAlpha(result);
        double gap = Math.abs(targetTotalAlpha - currentAlpha);

        log.debug("[深度优化] 开始深度总信度优化，当前差距: {:.4f}", gap);

        if (gap > tolerance) {
            // 深度策略1：全面数据重构
            if (gap > 0.03) {
                result = comprehensiveDataRestructuring(result, targetTotalAlpha, scaleLevel);
            }

            // 深度策略2：智能相关性调整
            result = intelligentCorrelationAdjustment(result, targetTotalAlpha, scaleLevel);

            // 深度策略3：精确微调
            result = precisionFineTuning(result, targetTotalAlpha, tolerance, scaleLevel);
        }

        double finalAlpha = calculateCronbachAlpha(result);
        log.debug("[深度优化] 深度优化完成: {:.4f} -> {:.4f}", currentAlpha, finalAlpha);

        return result;
    }

    /**
     * 全面数据重构
     */
    private List<List<Double>> comprehensiveDataRestructuring(List<List<Double>> data,
                                                             Double targetTotalAlpha,
                                                             Integer scaleLevel) {

        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        // 计算当前相关性矩阵
        double[][] correlationMatrix = calculateCorrelationMatrix(result);

        // 计算目标相关性水平
        double currentAlpha = calculateCronbachAlpha(result);
        double targetCorrelationLevel = estimateRequiredCorrelationLevel(currentAlpha, targetTotalAlpha, result.size());

        log.debug("[数据重构] 目标相关性水平: {:.4f}", targetCorrelationLevel);

        // 重构数据以达到目标相关性
        for (int itemIdx = 0; itemIdx < result.size(); itemIdx++) {
            List<Double> item = result.get(itemIdx);

            // 计算该项目与其他项目的平均相关性
            double avgCorrelation = 0.0;
            for (int otherIdx = 0; otherIdx < result.size(); otherIdx++) {
                if (otherIdx != itemIdx) {
                    avgCorrelation += Math.abs(correlationMatrix[itemIdx][otherIdx]);
                }
            }
            avgCorrelation /= (result.size() - 1);

            // 如果相关性不足，进行重构
            if (avgCorrelation < targetCorrelationLevel) {
                double adjustmentStrength = (targetCorrelationLevel - avgCorrelation) * 2.0;
                item = restructureItemForCorrelation(item, result, itemIdx, adjustmentStrength, scaleLevel);
                result.set(itemIdx, item);
            }
        }

        return result;
    }

    /**
     * 估算所需的相关性水平
     */
    private double estimateRequiredCorrelationLevel(double currentAlpha, double targetAlpha, int numItems) {
        // 基于Spearman-Brown公式的逆向计算
        double currentAvgCorr = currentAlpha / (numItems - (numItems - 1) * currentAlpha);
        double targetAvgCorr = targetAlpha / (numItems - (numItems - 1) * targetAlpha);

        return Math.max(0.1, Math.min(0.8, targetAvgCorr));
    }

    /**
     * 重构项目以提高相关性
     */
    private List<Double> restructureItemForCorrelation(List<Double> item,
                                                      List<List<Double>> allItems,
                                                      int itemIndex,
                                                      double strength,
                                                      Integer scaleLevel) {

        List<Double> result = new ArrayList<>(item);

        // 计算其他项目的平均模式
        List<Double> averagePattern = new ArrayList<>();
        for (int i = 0; i < item.size(); i++) {
            double sum = 0.0;
            int count = 0;
            for (int otherIdx = 0; otherIdx < allItems.size(); otherIdx++) {
                if (otherIdx != itemIndex) {
                    sum += allItems.get(otherIdx).get(i);
                    count++;
                }
            }
            averagePattern.add(count > 0 ? sum / count : item.get(i));
        }

        // 让当前项目向平均模式靠拢
        for (int i = 0; i < result.size(); i++) {
            double currentValue = result.get(i);
            double targetValue = averagePattern.get(i);
            double adjustment = (targetValue - currentValue) * Math.min(0.6, strength);
            double newValue = currentValue + adjustment;

            newValue = Math.max(1.0, Math.min((double)scaleLevel, newValue));
            result.set(i, newValue);
        }

        return result;
    }

    /**
     * 智能相关性调整
     */
    private List<List<Double>> intelligentCorrelationAdjustment(List<List<Double>> data,
                                                               Double targetTotalAlpha,
                                                               Integer scaleLevel) {

        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        double currentAlpha = calculateCronbachAlpha(result);
        double gap = targetTotalAlpha - currentAlpha;

        if (Math.abs(gap) > 0.01) {
            if (gap > 0) {
                // 需要提高信度：智能增强相关性
                result = intelligentEnhanceCorrelations(result, gap, scaleLevel);
            } else {
                // 需要降低信度：智能降低相关性
                result = intelligentReduceCorrelations(result, Math.abs(gap), scaleLevel);
            }
        }

        return result;
    }

    /**
     * 智能增强相关性
     */
    private List<List<Double>> intelligentEnhanceCorrelations(List<List<Double>> data,
                                                             double gap,
                                                             Integer scaleLevel) {

        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        // 计算当前相关性矩阵
        double[][] correlationMatrix = calculateCorrelationMatrix(result);

        // 找出相关性最低的项目对进行重点增强
        List<ItemPair> lowCorrelationPairs = findLowCorrelationPairs(correlationMatrix, 0.3);

        for (ItemPair pair : lowCorrelationPairs) {
            if (lowCorrelationPairs.indexOf(pair) >= 5) break; // 只处理前5对

            // 增强这两个项目的相关性
            enhancePairCorrelation(result, pair.item1, pair.item2, gap * 0.5, scaleLevel);
        }

        return result;
    }

    /**
     * 智能降低相关性
     */
    private List<List<Double>> intelligentReduceCorrelations(List<List<Double>> data,
                                                            double gap,
                                                            Integer scaleLevel) {

        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        // 计算当前相关性矩阵
        double[][] correlationMatrix = calculateCorrelationMatrix(result);

        // 找出相关性最高的项目对进行重点降低
        List<ItemPair> highCorrelationPairs = findHighCorrelationPairs(correlationMatrix, 0.7);

        for (ItemPair pair : highCorrelationPairs) {
            if (highCorrelationPairs.indexOf(pair) >= 3) break; // 只处理前3对

            // 降低这两个项目的相关性
            reducePairCorrelation(result, pair.item1, pair.item2, gap * 0.4, scaleLevel);
        }

        return result;
    }

    /**
     * 精确微调
     */
    private List<List<Double>> precisionFineTuning(List<List<Double>> data,
                                                  Double targetTotalAlpha,
                                                  Double tolerance,
                                                  Integer scaleLevel) {

        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        int maxMicroIterations = 10;

        for (int iter = 0; iter < maxMicroIterations; iter++) {
            double currentAlpha = calculateCronbachAlpha(result);
            double gap = targetTotalAlpha - currentAlpha;

            if (Math.abs(gap) <= tolerance) {
                break;
            }

            // 微调强度：非常小的调整
            double microStrength = Math.min(0.05, Math.abs(gap) * 0.3);

            if (gap > 0) {
                // 微量增强相关性
                result = enhanceGlobalCorrelations(result, microStrength, scaleLevel);
            } else {
                // 微量增加差异
                result = addGlobalDiversity(result, microStrength, scaleLevel);
            }
        }

        return result;
    }

    /**
     * 项目对类
     */
    private static class ItemPair {
        int item1;
        int item2;
        double correlation;

        ItemPair(int item1, int item2, double correlation) {
            this.item1 = item1;
            this.item2 = item2;
            this.correlation = correlation;
        }
    }

    /**
     * 找出低相关性项目对
     */
    private List<ItemPair> findLowCorrelationPairs(double[][] correlationMatrix, double threshold) {
        List<ItemPair> pairs = new ArrayList<>();

        for (int i = 0; i < correlationMatrix.length - 1; i++) {
            for (int j = i + 1; j < correlationMatrix.length; j++) {
                double correlation = Math.abs(correlationMatrix[i][j]);
                if (correlation < threshold) {
                    pairs.add(new ItemPair(i, j, correlation));
                }
            }
        }

        // 按相关性从低到高排序
        pairs.sort((a, b) -> Double.compare(a.correlation, b.correlation));

        return pairs;
    }

    /**
     * 找出高相关性项目对
     */
    private List<ItemPair> findHighCorrelationPairs(double[][] correlationMatrix, double threshold) {
        List<ItemPair> pairs = new ArrayList<>();

        for (int i = 0; i < correlationMatrix.length - 1; i++) {
            for (int j = i + 1; j < correlationMatrix.length; j++) {
                double correlation = Math.abs(correlationMatrix[i][j]);
                if (correlation > threshold) {
                    pairs.add(new ItemPair(i, j, correlation));
                }
            }
        }

        // 按相关性从高到低排序
        pairs.sort((a, b) -> Double.compare(b.correlation, a.correlation));

        return pairs;
    }

    /**
     * 增强项目对相关性
     */
    private void enhancePairCorrelation(List<List<Double>> data, int item1, int item2,
                                       double strength, Integer scaleLevel) {

        List<Double> data1 = data.get(item1);
        List<Double> data2 = data.get(item2);

        for (int i = 0; i < data1.size(); i++) {
            if (Math.random() < strength) {
                double val1 = data1.get(i);
                double val2 = data2.get(i);
                double avg = (val1 + val2) / 2.0;

                // 让两个值向平均值靠拢
                double newVal1 = val1 + (avg - val1) * 0.3;
                double newVal2 = val2 + (avg - val2) * 0.3;

                newVal1 = Math.max(1.0, Math.min((double)scaleLevel, newVal1));
                newVal2 = Math.max(1.0, Math.min((double)scaleLevel, newVal2));

                data1.set(i, newVal1);
                data2.set(i, newVal2);
            }
        }
    }

    /**
     * 降低项目对相关性
     */
    private void reducePairCorrelation(List<List<Double>> data, int item1, int item2,
                                      double strength, Integer scaleLevel) {

        List<Double> data1 = data.get(item1);
        List<Double> data2 = data.get(item2);
        Random random = new Random(42);

        for (int i = 0; i < data1.size(); i++) {
            if (Math.random() < strength) {
                double val1 = data1.get(i);
                double val2 = data2.get(i);

                // 让两个值更加不同
                double noise1 = random.nextGaussian() * 0.3;
                double noise2 = -noise1; // 反向噪声

                double newVal1 = val1 + noise1;
                double newVal2 = val2 + noise2;

                newVal1 = Math.max(1.0, Math.min((double)scaleLevel, newVal1));
                newVal2 = Math.max(1.0, Math.min((double)scaleLevel, newVal2));

                data1.set(i, newVal1);
                data2.set(i, newVal2);
            }
        }
    }

    /**
     * 计算标准差的辅助方法
     */
    private double calculateStandardDeviation(List<Double> values) {
        if (values.size() < 2) return 0.0;

        double mean = values.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
        double variance = values.stream()
                .mapToDouble(v -> Math.pow(v - mean, 2))
                .average()
                .orElse(0.0);

        return Math.sqrt(variance);
    }

    /**
     * 协调维度信度
     */
    private List<List<Double>> coordinateDimensionReliabilities(List<List<Double>> data,
                                                              List<List<Integer>> dimensions,
                                                              List<Double> targetDimensionAlphas,
                                                              Double tolerance,
                                                              List<Integer> allColumns,
                                                              Integer scaleLevel) {

        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        for (int dimIdx = 0; dimIdx < dimensions.size(); dimIdx++) {
            List<Integer> dimension = dimensions.get(dimIdx);
            double targetAlpha = targetDimensionAlphas.get(dimIdx);

            // 提取维度数据
            List<List<Double>> dimensionData = new ArrayList<>();
            List<Integer> dimensionDataIndices = new ArrayList<>();
            for (Integer col : dimension) {
                int dataIdx = getDataIndex(col, allColumns);
                dimensionData.add(new ArrayList<>(result.get(dataIdx)));
                dimensionDataIndices.add(dataIdx);
            }

            double currentAlpha = calculateCronbachAlpha(dimensionData);
            double alphaGap = targetAlpha - currentAlpha;

            if (Math.abs(alphaGap) > tolerance) {
                // 执行精细调整
                dimensionData = performPreciseAlphaAdjustment(dimensionData, alphaGap, scaleLevel);

                // 将调整后的数据放回
                for (int i = 0; i < dimension.size(); i++) {
                    result.set(dimensionDataIndices.get(i), dimensionData.get(i));
                }
            }
        }

        return result;
    }

    /**
     * 执行精确的信度调整
     */
    private List<List<Double>> performPreciseAlphaAdjustment(List<List<Double>> dimensionData,
                                                           double alphaGap,
                                                           Integer scaleLevel) {

        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : dimensionData) {
            result.add(new ArrayList<>(col));
        }

        double adjustmentStrength = Math.min(0.1, Math.abs(alphaGap) * 0.5);

        if (alphaGap > 0) {
            // 需要提高信度：精确增强相关性
            result = enhanceIntraDimensionCorrelations(result, adjustmentStrength, scaleLevel);
        } else {
            // 需要降低信度：精确增加差异性
            result = addControlledDiversity(result, adjustmentStrength, scaleLevel);
        }

        return result;
    }

    /**
     * 协调总信度 - 增强版本
     */
    private List<List<Double>> coordinateTotalReliability(List<List<Double>> data,
                                                         Double targetTotalAlpha,
                                                         Double tolerance,
                                                         Integer scaleLevel) {

        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        double currentTotalAlpha = calculateCronbachAlpha(result);
        double alphaGap = targetTotalAlpha - currentTotalAlpha;

        log.debug("[总信度协调] 开始协调总信度: 当前{:.4f}, 目标{:.4f}, 差距{:.4f}",
                 currentTotalAlpha, targetTotalAlpha, alphaGap);

        if (Math.abs(alphaGap) > tolerance) {
            // 使用多阶段迭代优化总信度
            result = optimizeTotalReliabilityIteratively(result, targetTotalAlpha, tolerance, scaleLevel, alphaGap);
        }

        double finalAlpha = calculateCronbachAlpha(result);
        log.debug("[总信度协调] 协调完成: {:.4f} -> {:.4f}, 差距{:.4f}",
                 currentTotalAlpha, finalAlpha, Math.abs(finalAlpha - targetTotalAlpha));

        return result;
    }

    /**
     * 多阶段迭代优化总信度 - 高精度版本
     */
    private List<List<Double>> optimizeTotalReliabilityIteratively(List<List<Double>> data,
                                                                  Double targetTotalAlpha,
                                                                  Double tolerance,
                                                                  Integer scaleLevel,
                                                                  double initialGap) {

        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        log.info("[总信度高精度优化] 开始高精度总信度优化，初始差距: {:.4f}", Math.abs(initialGap));

        // 根据差距大小动态确定策略
        if (Math.abs(initialGap) > 0.05) {
            // 大差距：使用激进策略
            result = optimizeTotalReliabilityAggressive(result, targetTotalAlpha, tolerance, scaleLevel);
        } else {
            // 小差距：使用精细策略
            result = optimizeTotalReliabilityPrecise(result, targetTotalAlpha, tolerance, scaleLevel);
        }

        // 最终救援机制：如果仍未达标，使用终极策略
        double finalAlpha = calculateCronbachAlpha(result);
        double finalGap = Math.abs(finalAlpha - targetTotalAlpha);

        if (finalGap > tolerance) {
            log.warn("[总信度救援] 常规优化未达标，启动救援机制，当前差距: {:.4f}", finalGap);
            result = totalReliabilityRescueMechanism(result, targetTotalAlpha, tolerance, scaleLevel);
        }

        double rescueAlpha = calculateCronbachAlpha(result);
        log.info("[总信度高精度优化] 优化完成: {:.4f} -> {:.4f}, 最终差距: {:.4f}",
                calculateCronbachAlpha(data), rescueAlpha, Math.abs(rescueAlpha - targetTotalAlpha));

        return result;
    }

    /**
     * 激进策略优化总信度（用于大差距情况）
     */
    private List<List<Double>> optimizeTotalReliabilityAggressive(List<List<Double>> data,
                                                                 Double targetTotalAlpha,
                                                                 Double tolerance,
                                                                 Integer scaleLevel) {

        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        int maxIterations = 30; // 大幅增加迭代次数
        List<List<Double>> bestResult = new ArrayList<>();
        for (List<Double> col : result) {
            bestResult.add(new ArrayList<>(col));
        }
        double bestGap = Math.abs(calculateCronbachAlpha(result) - targetTotalAlpha);

        log.debug("[激进优化] 开始激进优化，目标差距: {:.4f}", bestGap);

        for (int iter = 0; iter < maxIterations; iter++) {
            double currentAlpha = calculateCronbachAlpha(result);
            double currentGap = targetTotalAlpha - currentAlpha;
            double absGap = Math.abs(currentGap);

            // 更宽松的收敛条件，避免过早停止
            if (absGap <= tolerance * 0.8) {
                log.debug("[激进优化] 第{}次迭代达到目标: {:.4f}", iter + 1, currentAlpha);
                break;
            }

            // 激进调整强度：不设上限，根据差距动态调整
            double baseStrength = Math.min(0.6, absGap * 1.5); // 大幅提高强度上限
            double urgencyFactor = absGap > 0.1 ? 1.5 : 1.0; // 超大差距时增加紧急系数
            double adjustmentStrength = baseStrength * urgencyFactor;

            log.debug("[激进优化] 第{}次迭代: 当前{:.4f}, 差距{:.4f}, 强度{:.3f}",
                     iter + 1, currentAlpha, currentGap, adjustmentStrength);

            if (currentGap > 0) {
                // 需要提高总信度：使用激进增强策略
                result = enhanceTotalReliabilityAggressive(result, adjustmentStrength, scaleLevel, iter);
            } else {
                // 需要降低总信度：使用受控降低策略
                result = reduceTotalReliabilityControlled(result, adjustmentStrength * 0.7, scaleLevel, iter);
            }

            // 记录最佳结果
            double newAlpha = calculateCronbachAlpha(result);
            double newGap = Math.abs(newAlpha - targetTotalAlpha);
            if (newGap < bestGap) {
                bestGap = newGap;
                bestResult = new ArrayList<>();
                for (List<Double> col : result) {
                    bestResult.add(new ArrayList<>(col));
                }
            }

            // 每5次迭代进行数据稳定性检查
            if ((iter + 1) % 5 == 0) {
                result = ensureDataStabilityForTotalAlpha(result, scaleLevel);
            }
        }

        return bestResult;
    }

    /**
     * 精细策略优化总信度（用于小差距情况）
     */
    private List<List<Double>> optimizeTotalReliabilityPrecise(List<List<Double>> data,
                                                              Double targetTotalAlpha,
                                                              Double tolerance,
                                                              Integer scaleLevel) {

        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        int maxIterations = 20;
        double previousAlpha = calculateCronbachAlpha(result);

        log.debug("[精细优化] 开始精细优化，初始信度: {:.4f}", previousAlpha);

        for (int iter = 0; iter < maxIterations; iter++) {
            double currentAlpha = calculateCronbachAlpha(result);
            double currentGap = targetTotalAlpha - currentAlpha;
            double absGap = Math.abs(currentGap);

            if (absGap <= tolerance) {
                log.debug("[精细优化] 第{}次迭代达到目标: {:.4f}", iter + 1, currentAlpha);
                break;
            }

            // 精细调整强度：小步长，高精度
            double baseStrength = Math.min(0.15, absGap * 1.2);
            double precisionFactor = Math.max(0.5, 1.0 - (double)iter / maxIterations);
            double adjustmentStrength = baseStrength * precisionFactor;

            log.debug("[精细优化] 第{}次迭代: 当前{:.4f}, 差距{:.4f}, 强度{:.3f}",
                     iter + 1, currentAlpha, currentGap, adjustmentStrength);

            if (currentGap > 0) {
                // 精细增强总信度
                result = enhanceTotalReliabilityPrecise(result, adjustmentStrength, scaleLevel);
            } else {
                // 精细降低总信度
                result = reduceTotalReliabilityPrecise(result, adjustmentStrength, scaleLevel);
            }

            // 检查是否有改进
            double newAlpha = calculateCronbachAlpha(result);
            if (Math.abs(newAlpha - previousAlpha) < 0.001) {
                log.debug("[精细优化] 第{}次迭代改进微小，提前结束", iter + 1);
                break;
            }
            previousAlpha = newAlpha;
        }

        return result;
    }

    /**
     * 总信度救援机制（终极策略）
     */
    private List<List<Double>> totalReliabilityRescueMechanism(List<List<Double>> data,
                                                              Double targetTotalAlpha,
                                                              Double tolerance,
                                                              Integer scaleLevel) {

        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        double currentAlpha = calculateCronbachAlpha(result);
        double gap = targetTotalAlpha - currentAlpha;

        log.warn("[救援机制] 启动救援机制，当前信度: {:.4f}, 目标: {:.4f}, 差距: {:.4f}",
                currentAlpha, targetTotalAlpha, gap);

        if (gap > 0) {
            // 需要大幅提高总信度：使用极端策略
            result = extremeEnhanceTotalReliability(result, Math.abs(gap), scaleLevel);
        } else {
            // 需要大幅降低总信度：使用极端策略
            result = extremeReduceTotalReliability(result, Math.abs(gap), scaleLevel);
        }

        double rescueAlpha = calculateCronbachAlpha(result);
        log.warn("[救援机制] 救援完成: {:.4f} -> {:.4f}, 最终差距: {:.4f}",
                currentAlpha, rescueAlpha, Math.abs(rescueAlpha - targetTotalAlpha));

        return result;
    }

    /**
     * 激进增强总信度策略
     */
    private List<List<Double>> enhanceTotalReliabilityAggressive(List<List<Double>> data,
                                                                double strength,
                                                                Integer scaleLevel,
                                                                int iteration) {

        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        // 策略1：强力增强全局相关性
        result = enhanceGlobalCorrelations(result, strength * 0.8, scaleLevel);

        // 策略2：数据同质化处理
        result = homogenizeDataForReliability(result, strength * 0.6, scaleLevel);

        // 策略3：异常值强力修正
        if (iteration % 2 == 0) {
            result = aggressiveOutlierCorrection(result, strength * 0.5, scaleLevel);
        }

        // 策略4：增强样本一致性
        if (iteration % 3 == 0) {
            result = enhanceSampleConsistencyAggressive(result, strength * 0.4, scaleLevel);
        }

        return result;
    }

    /**
     * 数据同质化处理（增强相关性）
     */
    private List<List<Double>> homogenizeDataForReliability(List<List<Double>> data,
                                                           double strength,
                                                           Integer scaleLevel) {

        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        int sampleSize = result.get(0).size();

        // 计算全局均值模式
        List<Double> globalPattern = new ArrayList<>();
        for (int i = 0; i < sampleSize; i++) {
            double sum = 0.0;
            for (List<Double> item : result) {
                sum += item.get(i);
            }
            globalPattern.add(sum / result.size());
        }

        // 让所有项目向全局模式靠拢
        for (List<Double> item : result) {
            for (int i = 0; i < sampleSize; i++) {
                if (Math.random() < strength) {
                    double currentValue = item.get(i);
                    double targetValue = globalPattern.get(i);

                    // 强力向目标值靠拢
                    double adjustment = (targetValue - currentValue) * 0.7;
                    double newValue = currentValue + adjustment;

                    newValue = Math.max(1.0, Math.min((double)scaleLevel, newValue));
                    item.set(i, newValue);
                }
            }
        }

        return result;
    }

    /**
     * 激进异常值修正
     */
    private List<List<Double>> aggressiveOutlierCorrection(List<List<Double>> data,
                                                          double strength,
                                                          Integer scaleLevel) {

        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        // 对每个项目进行激进异常值修正
        for (List<Double> item : result) {
            List<Double> values = new ArrayList<>(item);
            double mean = values.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
            double std = calculateStandardDeviation(values);

            if (std > 0) {
                for (int i = 0; i < item.size(); i++) {
                    double value = item.get(i);
                    double zScore = (value - mean) / std;

                    // 激进处理异常值（z-score > 1.5）
                    if (Math.abs(zScore) > 1.5 && Math.random() < strength) {
                        double correction = -zScore * 0.8 * strength;
                        double newValue = value + correction;
                        newValue = Math.max(1.0, Math.min((double)scaleLevel, newValue));
                        item.set(i, newValue);
                    }
                }
            }
        }

        return result;
    }

    /**
     * 激进增强样本一致性
     */
    private List<List<Double>> enhanceSampleConsistencyAggressive(List<List<Double>> data,
                                                                 double strength,
                                                                 Integer scaleLevel) {

        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        int sampleSize = result.get(0).size();

        // 对每个样本进行激进一致性增强
        for (int sampleIdx = 0; sampleIdx < sampleSize; sampleIdx++) {
            if (Math.random() < strength) {
                List<Double> sampleValues = new ArrayList<>();
                for (List<Double> item : result) {
                    sampleValues.add(item.get(sampleIdx));
                }

                // 计算样本均值和标准差
                double sampleMean = sampleValues.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
                double sampleStd = calculateStandardDeviation(sampleValues);

                // 激进地让所有项目向均值靠拢
                for (int itemIdx = 0; itemIdx < result.size(); itemIdx++) {
                    double currentValue = result.get(itemIdx).get(sampleIdx);
                    double zScore = sampleStd > 0 ? (currentValue - sampleMean) / sampleStd : 0;

                    // 强力压缩差异
                    if (Math.abs(zScore) > 0.5) {
                        double adjustment = (sampleMean - currentValue) * 0.6;
                        double newValue = currentValue + adjustment;

                        newValue = Math.max(1.0, Math.min((double)scaleLevel, newValue));
                        result.get(itemIdx).set(sampleIdx, newValue);
                    }
                }
            }
        }

        return result;
    }

    /**
     * 精细增强总信度
     */
    private List<List<Double>> enhanceTotalReliabilityPrecise(List<List<Double>> data,
                                                             double strength,
                                                             Integer scaleLevel) {

        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        // 精细策略：小幅度多次调整
        result = enhanceGlobalCorrelations(result, strength * 0.5, scaleLevel);
        result = smoothExtremeValues(result, strength * 0.3, scaleLevel);
        result = enhanceInterItemConsistency(result, strength * 0.2, scaleLevel);

        return result;
    }

    /**
     * 精细降低总信度
     */
    private List<List<Double>> reduceTotalReliabilityPrecise(List<List<Double>> data,
                                                            double strength,
                                                            Integer scaleLevel) {

        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        // 精细策略：小幅度增加差异
        result = addGlobalDiversity(result, strength * 0.4, scaleLevel);
        result = addSelectiveItemUniqueness(result, strength * 0.3, scaleLevel);

        return result;
    }

    /**
     * 极端增强总信度策略（救援机制）
     */
    private List<List<Double>> extremeEnhanceTotalReliability(List<List<Double>> data,
                                                             double gap,
                                                             Integer scaleLevel) {

        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        log.warn("[极端增强] 使用极端策略增强总信度，差距: {:.4f}", gap);

        // 极端策略1：强制数据同质化
        result = forceDataHomogenization(result, Math.min(0.8, gap * 2.0), scaleLevel);

        // 极端策略2：消除所有异常值
        result = eliminateAllOutliers(result, scaleLevel);

        // 极端策略3：强制样本一致性
        result = forceSampleConsistency(result, Math.min(0.9, gap * 1.5), scaleLevel);

        return result;
    }

    /**
     * 极端降低总信度策略（救援机制）
     */
    private List<List<Double>> extremeReduceTotalReliability(List<List<Double>> data,
                                                            double gap,
                                                            Integer scaleLevel) {

        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        log.warn("[极端降低] 使用极端策略降低总信度，差距: {:.4f}", gap);

        // 极端策略1：强制增加差异性
        result = forceDataDiversification(result, Math.min(0.6, gap * 1.5), scaleLevel);

        // 极端策略2：引入随机噪声
        result = introduceRandomNoise(result, Math.min(0.4, gap * 1.0), scaleLevel);

        return result;
    }

    /**
     * 强制数据同质化
     */
    private List<List<Double>> forceDataHomogenization(List<List<Double>> data,
                                                      double strength,
                                                      Integer scaleLevel) {

        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        int sampleSize = result.get(0).size();

        // 计算每个样本的目标值（所有项目的均值）
        for (int sampleIdx = 0; sampleIdx < sampleSize; sampleIdx++) {
            List<Double> sampleValues = new ArrayList<>();
            for (List<Double> item : result) {
                sampleValues.add(item.get(sampleIdx));
            }

            double targetValue = sampleValues.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);

            // 强制所有项目向目标值靠拢
            for (int itemIdx = 0; itemIdx < result.size(); itemIdx++) {
                double currentValue = result.get(itemIdx).get(sampleIdx);
                double adjustment = (targetValue - currentValue) * strength;
                double newValue = currentValue + adjustment;

                newValue = Math.max(1.0, Math.min((double)scaleLevel, newValue));
                result.get(itemIdx).set(sampleIdx, newValue);
            }
        }

        return result;
    }

    /**
     * 消除所有异常值
     */
    private List<List<Double>> eliminateAllOutliers(List<List<Double>> data,
                                                   Integer scaleLevel) {

        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        // 对每个项目消除异常值
        for (List<Double> item : result) {
            List<Double> values = new ArrayList<>(item);
            double mean = values.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
            double std = calculateStandardDeviation(values);

            if (std > 0) {
                for (int i = 0; i < item.size(); i++) {
                    double value = item.get(i);
                    double zScore = (value - mean) / std;

                    // 强制修正所有偏离均值的值
                    if (Math.abs(zScore) > 1.0) {
                        double newValue = mean + Math.signum(zScore) * std * 0.8;
                        newValue = Math.max(1.0, Math.min((double)scaleLevel, newValue));
                        item.set(i, newValue);
                    }
                }
            }
        }

        return result;
    }

    /**
     * 强制样本一致性
     */
    private List<List<Double>> forceSampleConsistency(List<List<Double>> data,
                                                     double strength,
                                                     Integer scaleLevel) {

        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        int sampleSize = result.get(0).size();

        // 强制每个样本内的一致性
        for (int sampleIdx = 0; sampleIdx < sampleSize; sampleIdx++) {
            List<Double> sampleValues = new ArrayList<>();
            for (List<Double> item : result) {
                sampleValues.add(item.get(sampleIdx));
            }

            double sampleMean = sampleValues.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);

            // 强制所有项目向样本均值靠拢
            for (int itemIdx = 0; itemIdx < result.size(); itemIdx++) {
                double currentValue = result.get(itemIdx).get(sampleIdx);
                double adjustment = (sampleMean - currentValue) * strength;
                double newValue = currentValue + adjustment;

                newValue = Math.max(1.0, Math.min((double)scaleLevel, newValue));
                result.get(itemIdx).set(sampleIdx, newValue);
            }
        }

        return result;
    }

    /**
     * 强制数据差异化
     */
    private List<List<Double>> forceDataDiversification(List<List<Double>> data,
                                                       double strength,
                                                       Integer scaleLevel) {

        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        Random random = new Random(42);

        // 为每个项目强制添加独特性
        for (int itemIdx = 0; itemIdx < result.size(); itemIdx++) {
            List<Double> item = result.get(itemIdx);
            double itemFactor = (itemIdx + 1) * 0.3; // 每个项目有不同的差异化因子

            for (int i = 0; i < item.size(); i++) {
                double originalValue = item.get(i);
                double noise = random.nextGaussian() * strength * itemFactor;
                double newValue = originalValue + noise;

                newValue = Math.max(1.0, Math.min((double)scaleLevel, newValue));
                item.set(i, newValue);
            }
        }

        return result;
    }

    /**
     * 引入随机噪声
     */
    private List<List<Double>> introduceRandomNoise(List<List<Double>> data,
                                                   double strength,
                                                   Integer scaleLevel) {

        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        Random random = new Random(System.currentTimeMillis());

        // 为所有数据点添加随机噪声
        for (List<Double> item : result) {
            for (int i = 0; i < item.size(); i++) {
                if (Math.random() < 0.6) { // 60%的数据点添加噪声
                    double originalValue = item.get(i);
                    double noise = random.nextGaussian() * strength;
                    double newValue = originalValue + noise;

                    newValue = Math.max(1.0, Math.min((double)scaleLevel, newValue));
                    item.set(i, newValue);
                }
            }
        }

        return result;
    }

    /**
     * 多策略增强总信度
     */
    private List<List<Double>> enhanceTotalReliabilityMultiStrategy(List<List<Double>> data,
                                                                   double strength,
                                                                   Integer scaleLevel,
                                                                   int iteration) {

        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        // 策略1：增强全局相关性（主要策略）
        result = enhanceGlobalCorrelations(result, strength * 0.6, scaleLevel);

        // 策略2：减少极端值差异（辅助策略）
        if (iteration % 2 == 0) {
            result = smoothExtremeValues(result, strength * 0.3, scaleLevel);
        }

        // 策略3：增强项目间一致性（每3次迭代执行一次）
        if (iteration % 3 == 0) {
            result = enhanceInterItemConsistency(result, strength * 0.4, scaleLevel);
        }

        return result;
    }

    /**
     * 受控降低总信度
     */
    private List<List<Double>> reduceTotalReliabilityControlled(List<List<Double>> data,
                                                               double strength,
                                                               Integer scaleLevel,
                                                               int iteration) {

        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        // 策略1：适度增加差异性（主要策略）
        result = addGlobalDiversity(result, strength * 0.5, scaleLevel);

        // 策略2：增加部分项目的独特性（每2次迭代执行一次）
        if (iteration % 2 == 0) {
            result = addSelectiveItemUniqueness(result, strength * 0.3, scaleLevel);
        }

        return result;
    }

    /**
     * 确保总信度优化的数据稳定性
     */
    private List<List<Double>> ensureDataStabilityForTotalAlpha(List<List<Double>> data,
                                                               Integer scaleLevel) {

        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        // 检查并修正异常值
        for (List<Double> item : result) {
            for (int i = 0; i < item.size(); i++) {
                double value = item.get(i);
                if (value < 1.0 || value > scaleLevel) {
                    value = Math.max(1.0, Math.min((double)scaleLevel, value));
                    item.set(i, value);
                }
            }
        }

        // 确保基本的数据变异性
        result = ensureDataVariabilityOptimized(result);

        return result;
    }

    /**
     * 增强全局相关性
     */
    private List<List<Double>> enhanceGlobalCorrelations(List<List<Double>> data,
                                                        double strength,
                                                        Integer scaleLevel) {

        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        int numItems = result.size();
        int sampleSize = result.get(0).size();

        // 计算全局共同因子
        List<Double> globalFactor = new ArrayList<>();
        for (int i = 0; i < sampleSize; i++) {
            double sum = 0.0;
            for (List<Double> item : result) {
                sum += item.get(i);
            }
            globalFactor.add(sum / numItems);
        }

        // 让所有题目轻微向全局共同因子靠拢
        for (List<Double> item : result) {
            for (int i = 0; i < sampleSize; i++) {
                if (Math.random() < 0.2) { // 20%的数据点进行调整
                    double originalValue = item.get(i);
                    double globalValue = globalFactor.get(i);

                    double adjustment = (globalValue - originalValue) * strength;
                    double newValue = originalValue + adjustment;

                    newValue = Math.max(1.0, Math.min((double)scaleLevel, newValue));
                    item.set(i, newValue);
                }
            }
        }

        return result;
    }

    /**
     * 平滑极端值以增强一致性
     */
    private List<List<Double>> smoothExtremeValues(List<List<Double>> data,
                                                  double strength,
                                                  Integer scaleLevel) {

        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        // 计算每个样本的总分
        int sampleSize = result.get(0).size();
        List<Double> sampleTotals = new ArrayList<>();
        for (int i = 0; i < sampleSize; i++) {
            double total = 0.0;
            for (List<Double> item : result) {
                total += item.get(i);
            }
            sampleTotals.add(total);
        }

        // 计算总分的均值和标准差
        DescriptiveStatistics totalStats = new DescriptiveStatistics();
        sampleTotals.forEach(totalStats::addValue);
        double meanTotal = totalStats.getMean();
        double stdTotal = totalStats.getStandardDeviation();

        // 对每个项目进行平滑处理
        for (List<Double> item : result) {
            for (int i = 0; i < sampleSize; i++) {
                double currentValue = item.get(i);
                double sampleTotal = sampleTotals.get(i);

                // 如果样本总分偏离均值较远，则进行平滑
                double zScore = Math.abs((sampleTotal - meanTotal) / stdTotal);
                if (zScore > 1.5 && Math.random() < strength) {
                    // 向样本均值方向调整
                    double expectedValue = meanTotal / result.size();
                    double adjustment = (expectedValue - currentValue) * 0.3;
                    double newValue = currentValue + adjustment;

                    newValue = Math.max(1.0, Math.min((double)scaleLevel, newValue));
                    item.set(i, newValue);
                }
            }
        }

        return result;
    }

    /**
     * 增强项目间一致性
     */
    private List<List<Double>> enhanceInterItemConsistency(List<List<Double>> data,
                                                          double strength,
                                                          Integer scaleLevel) {

        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        int sampleSize = result.get(0).size();

        // 对每个样本进行一致性增强
        for (int sampleIdx = 0; sampleIdx < sampleSize; sampleIdx++) {
            if (Math.random() < strength) {
                List<Double> sampleValues = new ArrayList<>();
                for (List<Double> item : result) {
                    sampleValues.add(item.get(sampleIdx));
                }

                // 计算样本均值
                double sampleMean = sampleValues.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);

                // 让所有项目向均值靠拢
                for (int itemIdx = 0; itemIdx < result.size(); itemIdx++) {
                    double currentValue = result.get(itemIdx).get(sampleIdx);
                    double adjustment = (sampleMean - currentValue) * 0.2;
                    double newValue = currentValue + adjustment;

                    newValue = Math.max(1.0, Math.min((double)scaleLevel, newValue));
                    result.get(itemIdx).set(sampleIdx, newValue);
                }
            }
        }

        return result;
    }

    /**
     * 为选定项目添加独特性
     */
    private List<List<Double>> addSelectiveItemUniqueness(List<List<Double>> data,
                                                         double strength,
                                                         Integer scaleLevel) {

        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        Random random = new Random(42);

        // 随机选择30%的项目添加独特性
        int numItemsToModify = Math.max(1, (int)(result.size() * 0.3));
        List<Integer> selectedItems = new ArrayList<>();
        for (int i = 0; i < numItemsToModify; i++) {
            int itemIdx = random.nextInt(result.size());
            if (!selectedItems.contains(itemIdx)) {
                selectedItems.add(itemIdx);
            }
        }

        // 为选定项目添加独特的变异
        for (Integer itemIdx : selectedItems) {
            List<Double> item = result.get(itemIdx);
            for (int i = 0; i < item.size(); i++) {
                if (Math.random() < strength) {
                    double originalValue = item.get(i);
                    double noise = random.nextGaussian() * 0.5;
                    double newValue = originalValue + noise;

                    newValue = Math.max(1.0, Math.min((double)scaleLevel, newValue));
                    item.set(i, newValue);
                }
            }
        }

        return result;
    }

    /**
     * 添加全局差异性
     */
    private List<List<Double>> addGlobalDiversity(List<List<Double>> data,
                                                 double strength,
                                                 Integer scaleLevel) {

        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        Random random = new Random(42);

        // 为每个题目添加独特的全局变异
        for (int itemIdx = 0; itemIdx < result.size(); itemIdx++) {
            List<Double> item = result.get(itemIdx);

            for (int i = 0; i < item.size(); i++) {
                if (Math.random() < 0.15) { // 15%的数据点进行调整
                    double originalValue = item.get(i);
                    double noise = random.nextGaussian() * strength;
                    double newValue = originalValue + noise;

                    newValue = Math.max(1.0, Math.min((double)scaleLevel, newValue));
                    item.set(i, newValue);
                }
            }
        }

        return result;
    }

    /**
     * 协调KMO值
     */
    private List<List<Double>> coordinateKMO(List<List<Double>> data,
                                            Double targetKMO,
                                            Double tolerance,
                                            Integer scaleLevel) {

        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        double currentKMO = calculateKMOFromAdjustedData(result);
        if (Double.isNaN(currentKMO)) {
            return result;
        }

        double kmoGap = targetKMO - currentKMO;

        if (Math.abs(kmoGap) > tolerance) {
            double adjustmentStrength = Math.min(0.05, Math.abs(kmoGap) * 0.2);

            if (kmoGap > 0) {
                // 需要提高KMO：增强相关性
                result = enhanceGlobalCorrelations(result, adjustmentStrength, scaleLevel);
            } else {
                // 需要降低KMO：增加差异性
                result = addGlobalDiversity(result, adjustmentStrength, scaleLevel);
            }
        }

        return result;
    }

    /**
     * 协调题目均值
     */
    private List<List<Double>> coordinateItemMeans(List<List<Double>> data,
                                                  List<List<Integer>> dimensions,
                                                  List<List<Double>> targetItemMeans,
                                                  List<Integer> allColumns,
                                                  Integer scaleLevel) {

        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        for (int dimIdx = 0; dimIdx < Math.min(dimensions.size(), targetItemMeans.size()); dimIdx++) {
            List<Integer> dimension = dimensions.get(dimIdx);
            List<Double> dimensionTargetMeans = targetItemMeans.get(dimIdx);

            if (dimensionTargetMeans == null || dimensionTargetMeans.isEmpty()) {
                continue;
            }

            for (int itemIdx = 0; itemIdx < Math.min(dimension.size(), dimensionTargetMeans.size()); itemIdx++) {
                Integer col = dimension.get(itemIdx);
                Double targetMean = dimensionTargetMeans.get(itemIdx);

                if (targetMean != null) {
                    int dataIdx = getDataIndex(col, allColumns);
                    List<Double> item = result.get(dataIdx);

                    // 轻微调整均值
                    item = adjustSingleItemMeanGently(item, targetMean, scaleLevel);
                    result.set(dataIdx, item);
                }
            }
        }

        return result;
    }

    /**
     * 轻微调整单个题目均值
     */
    private List<Double> adjustSingleItemMeanGently(List<Double> item, Double targetMean, Integer scaleLevel) {
        List<Double> result = new ArrayList<>(item);

        DescriptiveStatistics stats = new DescriptiveStatistics();
        result.forEach(stats::addValue);
        double currentMean = stats.getMean();
        double gap = targetMean - currentMean;

        if (Math.abs(gap) > 0.05) { // 只有偏差较大时才调整
            // 使用温和的线性调整
            for (int i = 0; i < result.size(); i++) {
                if (Math.random() < 0.3) { // 30%的数据点进行调整
                    double newValue = result.get(i) + gap * 0.3;
                    newValue = Math.max(1.0, Math.min((double)scaleLevel, newValue));
                    result.set(i, newValue);
                }
            }
        }

        return result;
    }

    /**
     * 最终因子结构微调
     */
    private List<List<Double>> performFinalFactorStructureTuning(List<List<Double>> data,
                                                               List<List<Integer>> dimensions,
                                                               Integer scaleLevel) {

        log.debug("[因子结构微调] 开始最终因子结构微调");

        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        List<Integer> allColumns = getAllColumns(dimensions);

        // 微调维度间的区分度
        for (int dim1 = 0; dim1 < dimensions.size() - 1; dim1++) {
            for (int dim2 = dim1 + 1; dim2 < dimensions.size(); dim2++) {
                result = enhanceDimensionSeparation(result, dimensions.get(dim1), dimensions.get(dim2),
                                                  allColumns, scaleLevel);
            }
        }

        // 微调维度内的聚集性
        for (List<Integer> dimension : dimensions) {
            result = enhanceDimensionCohesion(result, dimension, allColumns, scaleLevel);
        }

        log.debug("[因子结构微调] 最终因子结构微调完成");
        return result;
    }

    /**
     * 增强维度间的区分度
     */
    private List<List<Double>> enhanceDimensionSeparation(List<List<Double>> data,
                                                         List<Integer> dimension1,
                                                         List<Integer> dimension2,
                                                         List<Integer> allColumns,
                                                         Integer scaleLevel) {

        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        // 轻微降低两个维度间的相关性
        for (Integer col1 : dimension1) {
            for (Integer col2 : dimension2) {
                int idx1 = getDataIndex(col1, allColumns);
                int idx2 = getDataIndex(col2, allColumns);

                List<Double> item1 = result.get(idx1);
                List<Double> item2 = result.get(idx2);

                // 轻微增加差异
                for (int i = 0; i < item1.size(); i++) {
                    if (Math.random() < 0.1) { // 10%的数据点进行调整
                        double val1 = item1.get(i);
                        double val2 = item2.get(i);

                        // 让两个值更加不同
                        double diff = val1 - val2;
                        double enhancement = Math.signum(diff) * 0.1;

                        double newVal1 = val1 + enhancement;
                        double newVal2 = val2 - enhancement;

                        newVal1 = Math.max(1.0, Math.min((double)scaleLevel, newVal1));
                        newVal2 = Math.max(1.0, Math.min((double)scaleLevel, newVal2));

                        item1.set(i, newVal1);
                        item2.set(i, newVal2);
                    }
                }
            }
        }

        return result;
    }

    /**
     * 增强维度内的聚集性
     */
    private List<List<Double>> enhanceDimensionCohesion(List<List<Double>> data,
                                                       List<Integer> dimension,
                                                       List<Integer> allColumns,
                                                       Integer scaleLevel) {

        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        // 提取维度数据
        List<List<Double>> dimensionData = new ArrayList<>();
        List<Integer> dimensionDataIndices = new ArrayList<>();
        for (Integer col : dimension) {
            int dataIdx = getDataIndex(col, allColumns);
            dimensionData.add(new ArrayList<>(result.get(dataIdx)));
            dimensionDataIndices.add(dataIdx);
        }

        // 轻微增强维度内相关性
        dimensionData = enhanceIntraDimensionCorrelations(dimensionData, 0.05, scaleLevel);

        // 将调整后的数据放回
        for (int i = 0; i < dimension.size(); i++) {
            result.set(dimensionDataIndices.get(i), dimensionData.get(i));
        }

        return result;
    }

    /**
     * 检查全局收敛
     */
    private boolean checkGlobalConvergence(List<List<Double>> data,
                                         List<List<Integer>> dimensions,
                                         List<Double> targetDimensionAlphas,
                                         Double targetTotalAlpha,
                                         Double targetKMO,
                                         Double tolerance) {

        try {
            List<Integer> allColumns = getAllColumns(dimensions);

            // 检查维度信度
            for (int dimIdx = 0; dimIdx < dimensions.size(); dimIdx++) {
                List<Integer> dimension = dimensions.get(dimIdx);
                double targetAlpha = targetDimensionAlphas.get(dimIdx);

                List<List<Double>> dimensionData = new ArrayList<>();
                for (Integer col : dimension) {
                    int dataIdx = getDataIndex(col, allColumns);
                    dimensionData.add(data.get(dataIdx));
                }

                double currentAlpha = calculateCronbachAlpha(dimensionData);
                if (Math.abs(currentAlpha - targetAlpha) > tolerance) {
                    return false;
                }
            }

            // 检查总信度（使用更严格的标准）
            if (targetTotalAlpha != null) {
                double currentTotalAlpha = calculateCronbachAlpha(data);
                double totalAlphaGap = Math.abs(currentTotalAlpha - targetTotalAlpha);
                // 总信度使用更严格的容忍度（0.8倍）
                if (totalAlphaGap > tolerance * 0.8) {
                    log.debug("[收敛检查] 总信度未达标: 当前{:.4f}, 目标{:.4f}, 差距{:.4f}",
                             currentTotalAlpha, targetTotalAlpha, totalAlphaGap);
                    return false;
                }
            }

            // 检查KMO
            if (targetKMO != null) {
                double currentKMO = calculateKMOFromAdjustedData(data);
                if (!Double.isNaN(currentKMO) && Math.abs(currentKMO - targetKMO) > tolerance) {
                    return false;
                }
            }

            return true;

        } catch (Exception e) {
            log.warn("[收敛检查] 检查失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 调整单个题目均值的辅助方法
     */
    private List<Double> adjustSingleItemMean(List<Double> item, Double targetMean, Integer scaleLevel) {
        List<Double> result = new ArrayList<>(item);

        DescriptiveStatistics stats = new DescriptiveStatistics();
        result.forEach(stats::addValue);
        double currentMean = stats.getMean();
        double gap = targetMean - currentMean;

        if (Math.abs(gap) > 0.01) {
            // 使用线性变换调整均值
            for (int i = 0; i < result.size(); i++) {
                double newValue = result.get(i) + gap;
                newValue = Math.max(1.0, Math.min((double)scaleLevel, newValue));
                result.set(i, newValue);
            }
        }

        return result;
    }

    /**
     * 执行最终精细调整
     */
    private List<List<Double>> performFinalRefinement(List<List<Double>> data,
                                                     List<List<Integer>> dimensions,
                                                     List<Double> targetDimensionAlphas,
                                                     Double targetTotalAlpha,
                                                     Double targetKMO,
                                                     Double tolerance) {
        log.info("[最终精细调整] 开始执行最终精细调整");

        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        // 确保数据变异性
        result = ensureDataVariabilityOptimized(result);

        // 使用最小强度进行精细调整
        double refinementIntensity = 0.1;

        List<Integer> allColumns = getAllColumns(dimensions);

        // 精细调整各维度信度
        for (int dimIdx = 0; dimIdx < dimensions.size(); dimIdx++) {
            List<Integer> dimension = dimensions.get(dimIdx);
            double targetAlpha = targetDimensionAlphas.get(dimIdx);

            List<List<Double>> dimensionData = new ArrayList<>();
            for (Integer col : dimension) {
                int dataIdx = getDataIndex(col, allColumns);
                dimensionData.add(result.get(dataIdx));
            }

            double currentAlpha = calculateCronbachAlpha(dimensionData);
            if (Math.abs(currentAlpha - targetAlpha) > tolerance) {
                List<List<Double>> refinedData = adjustSingleDimensionWithIntelligentStrategy(
                        dimensionData, targetAlpha, tolerance, refinementIntensity, "维度" + (dimIdx + 1));

                // 将调整后的数据放回原位置
                for (int i = 0; i < dimension.size(); i++) {
                    Integer col = dimension.get(i);
                    int dataIdx = getDataIndex(col, allColumns);
                    result.set(dataIdx, refinedData.get(i));
                }
            }
        }

        // 最终确保数据变异性
        result = ensureDataVariabilityOptimized(result);

        log.info("[最终精细调整] 最终精细调整完成");
        return result;
    }

    /**
     * 记录最终结果 - 优化版本
     */
    private void logFinalResultsOptimized(List<List<Double>> data,
                                        List<List<Integer>> dimensions,
                                        List<Integer> allColumns,
                                        List<Double> targetDimensionAlphas,
                                        Double targetTotalAlpha,
                                        Double targetKMO,
                                        List<Double> scoreHistory) {
        log.info("[最终结果] ========== 智能调整最终结果 ==========");

        int achievedCount = 0;
        int totalCount = 0;

        // 记录各维度信度
        for (int dimIdx = 0; dimIdx < dimensions.size(); dimIdx++) {
            List<Integer> dimension = dimensions.get(dimIdx);
            double targetAlpha = targetDimensionAlphas.get(dimIdx);

            List<List<Double>> dimensionData = new ArrayList<>();
            for (Integer col : dimension) {
                int dataIdx = getDataIndex(col, allColumns);
                dimensionData.add(data.get(dataIdx));
            }

            double actualAlpha = calculateCronbachAlpha(dimensionData);
            boolean achieved = Math.abs(actualAlpha - targetAlpha) <= 0.02;
            String status = achieved ? "✓达标" : "未达标";
            if (achieved) achievedCount++;
            totalCount++;

            log.info("[最终结果] 维度{}信度: 实际{:.3f}, 目标{:.3f}, 差距{:.3f} - {}",
                    dimIdx + 1, actualAlpha, targetAlpha, Math.abs(actualAlpha - targetAlpha), status);
        }

        // 记录总信度
        if (targetTotalAlpha != null) {
            double actualTotalAlpha = calculateCronbachAlpha(data);
            boolean achieved = Math.abs(actualTotalAlpha - targetTotalAlpha) <= 0.02;
            String status = achieved ? "✓达标" : "未达标";
            if (achieved) achievedCount++;
            totalCount++;

            log.info("[最终结果] 总信度: 实际{:.3f}, 目标{:.3f}, 差距{:.3f} - {}",
                    actualTotalAlpha, targetTotalAlpha, Math.abs(actualTotalAlpha - targetTotalAlpha), status);
        }

        // 记录KMO
        if (targetKMO != null) {
            double actualKMO = calculateKMOFromAdjustedData(data);
            boolean achieved = (!Double.isNaN(actualKMO) && Math.abs(actualKMO - targetKMO) <= 0.02);
            String status = achieved ? "✓达标" : "未达标";
            if (achieved) achievedCount++;
            totalCount++;

            log.info("[最终结果] KMO: 实际{:.3f}, 目标{:.3f}, 差距{:.3f} - {}",
                    actualKMO, targetKMO, Double.isNaN(actualKMO) ? Double.NaN : Math.abs(actualKMO - targetKMO), status);
        }

        log.info("[最终结果] 总体达成率: {}/{} ({:.1f}%)", achievedCount, totalCount,
                (double) achievedCount / totalCount * 100);

        // 记录收敛历史
        if (scoreHistory.size() > 1) {
            double initialScore = scoreHistory.get(0);
            double finalScore = scoreHistory.get(scoreHistory.size() - 1);
            double improvement = initialScore - finalScore;
            log.info("[最终结果] 收敛情况: 初始得分{:.4f} -> 最终得分{:.4f}, 改进{:.4f}",
                    initialScore, finalScore, improvement);
        }

        log.info("[最终结果] ========================================");
    }

    /**
     * 计算动态调整强度 - 防止过度调整的关键
     */
    private double calculateAdjustmentIntensity(int iteration, int consecutiveNoImprovement, int maxIterations) {
        // 基础强度随迭代次数递减
        double baseIntensity = Math.max(0.1, 1.0 - (double) iteration / maxIterations);

        // 如果连续无改进，降低强度
        if (consecutiveNoImprovement > 0) {
            baseIntensity *= Math.pow(0.8, consecutiveNoImprovement);
        }

        // 确保强度在合理范围内
        return Math.max(0.05, Math.min(1.0, baseIntensity));
    }


    /**
     * 检查得分是否出现震荡
     */
    private boolean checkForOscillation(List<Double> scoreHistory, int windowSize) {
        if (scoreHistory.size() < windowSize) {
            return false;
        }

        // 取最近的得分
        List<Double> recentScores = scoreHistory.subList(scoreHistory.size() - windowSize, scoreHistory.size());

        // 计算得分的标准差
        double mean = recentScores.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
        double variance = recentScores.stream().mapToDouble(score -> Math.pow(score - mean, 2)).average().orElse(0.0);
        double stdDev = Math.sqrt(variance);

        // 如果标准差很小但没有明显的下降趋势，认为是震荡
        if (stdDev < 0.01) {
            double firstScore = recentScores.get(0);
            double lastScore = recentScores.get(recentScores.size() - 1);
            return Math.abs(lastScore - firstScore) < 0.005; // 总体变化很小
        }

        return false;
    }

    /**
     * 检查核心目标是否达成 - 优先保证信度和KMO
     */
    private boolean checkAllTargetsAchievedOptimized(List<List<Double>> data, List<List<Integer>> dimensions,
                                          List<Integer> allColumns, List<Double> targetDimensionAlphas,
                                          Double targetTotalAlpha, Double targetKMO, Double tolerance) {

        log.debug("[核心目标检查] 开始检查核心指标（信度和KMO），均值为次要指标");

        // 核心指标1：各维度信度（必须达标）
        boolean allDimensionAlphasAchieved = true;
        for (int dimIdx = 0; dimIdx < dimensions.size(); dimIdx++) {
            List<Integer> dimension = dimensions.get(dimIdx);
            double targetAlpha = targetDimensionAlphas.get(dimIdx);

            List<List<Double>> dimensionData = new ArrayList<>();
            for (Integer col : dimension) {
                int dataIdx = getDataIndex(col, allColumns);
                dimensionData.add(data.get(dataIdx));
            }

            double actualAlpha = calculateCronbachAlpha(dimensionData);
            double deviation = Math.abs(actualAlpha - targetAlpha);

            if (deviation > tolerance) {
                allDimensionAlphasAchieved = false;
                log.debug("[核心目标检查] 维度{}信度未达标: 实际{:.4f}, 目标{:.4f}, 差距{:.4f} [关键指标]",
                         dimIdx + 1, actualAlpha, targetAlpha, deviation);
            } else {
                log.debug("[核心目标检查] 维度{}信度已达标: 实际{:.4f}, 目标{:.4f}, 差距{:.4f} ✓",
                         dimIdx + 1, actualAlpha, targetAlpha, deviation);
            }
        }

        // 核心指标2：总信度（必须达标）
        boolean totalAlphaAchieved = true;
        if (targetTotalAlpha != null) {
            double actualTotalAlpha = calculateCronbachAlpha(data);
            double deviation = Math.abs(actualTotalAlpha - targetTotalAlpha);

            if (deviation > tolerance) {
                totalAlphaAchieved = false;
                log.debug("[核心目标检查] 总信度未达标: 实际{:.4f}, 目标{:.4f}, 差距{:.4f} [关键指标]",
                         actualTotalAlpha, targetTotalAlpha, deviation);
            } else {
                log.debug("[核心目标检查] 总信度已达标: 实际{:.4f}, 目标{:.4f}, 差距{:.4f} ✓",
                         actualTotalAlpha, targetTotalAlpha, deviation);
            }
        }

        // 核心指标3：KMO（必须达标）
        boolean kmoAchieved = true;
        if (targetKMO != null) {
            double actualKMO = calculateKMOFromAdjustedData(data);

            if (Double.isNaN(actualKMO)) {
                kmoAchieved = false;
                log.debug("[核心目标检查] KMO为NaN，未达标 [关键指标]");
            } else {
                double deviation = Math.abs(actualKMO - targetKMO);
                if (deviation > tolerance) {
                    kmoAchieved = false;
                    log.debug("[核心目标检查] KMO未达标: 实际{:.4f}, 目标{:.4f}, 差距{:.4f} [关键指标]",
                             actualKMO, targetKMO, deviation);
                } else {
                    log.debug("[核心目标检查] KMO已达标: 实际{:.4f}, 目标{:.4f}, 差距{:.4f} ✓",
                             actualKMO, targetKMO, deviation);
                }
            }
        }

        // 只有所有核心指标都达标才算成功
        boolean allCoreTargetsAchieved = allDimensionAlphasAchieved && totalAlphaAchieved && kmoAchieved;

        if (allCoreTargetsAchieved) {
            log.info("[核心目标检查] ✓ 所有核心指标（维度信度、总信度、KMO）已达标");
        } else {
            log.debug("[核心目标检查] ✗ 核心指标未全部达标 - 维度信度:{}, 总信度:{}, KMO:{}",
                     allDimensionAlphasAchieved ? "✓" : "✗",
                     totalAlphaAchieved ? "✓" : "✗",
                     kmoAchieved ? "✓" : "✗");
        }

        return allCoreTargetsAchieved;
    }


    /**
     * 智能平衡综合得分 - 核心指标优先但兼顾均值
     */
    private double calculateOverallScoreWithMeanDeviation(List<List<Double>> data, List<List<Integer>> dimensions,
                                                        List<Integer> allColumns, List<Double> targetDimensionAlphas,
                                                        Double targetTotalAlpha, Double targetKMO,
                                                        List<List<Double>> targetItemMeans) {

        // 计算核心指标得分
        double coreScore = calculateCoreIndicatorsScore(data, dimensions, allColumns, targetDimensionAlphas, targetTotalAlpha, targetKMO);

        // 计算均值偏差得分
        double meanScore = calculateMeanDeviationScore(data, dimensions, allColumns, targetItemMeans);

        // 智能平衡权重：根据核心指标达标情况动态调整
        double coreWeight = calculateDynamicCoreWeight(data, dimensions, allColumns, targetDimensionAlphas, targetTotalAlpha, targetKMO);
        double meanWeight = 1.0 - coreWeight;

        // 综合得分：动态平衡
        double balancedScore = coreScore * coreWeight + meanScore * meanWeight;

        log.debug("[智能平衡] 核心得分: {:.4f}, 均值得分: {:.4f}, 核心权重: {:.3f}, 均值权重: {:.3f}, 综合得分: {:.4f}",
                 coreScore, meanScore, coreWeight, meanWeight, balancedScore);

        return balancedScore;
    }

    /**
     * 计算核心指标得分
     */
    private double calculateCoreIndicatorsScore(List<List<Double>> data, List<List<Integer>> dimensions,
                                              List<Integer> allColumns, List<Double> targetDimensionAlphas,
                                              Double targetTotalAlpha, Double targetKMO) {
        double totalScore = 0.0;
        int scoreComponents = 0;

        // 维度信度得分
        for (int dimIdx = 0; dimIdx < dimensions.size(); dimIdx++) {
            List<Integer> dimension = dimensions.get(dimIdx);
            double targetAlpha = targetDimensionAlphas.get(dimIdx);

            List<List<Double>> dimensionData = new ArrayList<>();
            for (Integer col : dimension) {
                int dataIdx = getDataIndex(col, allColumns);
                dimensionData.add(data.get(dataIdx));
            }

            double actualAlpha = calculateCronbachAlpha(dimensionData);
            double alphaGap = Math.abs(actualAlpha - targetAlpha);

            // 使用平方根函数，减少极端惩罚
            totalScore += Math.sqrt(alphaGap) * 50; // 适中权重
            scoreComponents++;
        }

        // 总信度得分（提高权重）
        if (targetTotalAlpha != null) {
            double actualTotalAlpha = calculateCronbachAlpha(data);
            double totalAlphaGap = Math.abs(actualTotalAlpha - targetTotalAlpha);
            totalScore += Math.sqrt(totalAlphaGap) * 60; // 提高权重，强调总信度重要性
            scoreComponents++;
        }

        // KMO得分
        if (targetKMO != null) {
            double actualKMO = calculateKMOFromAdjustedData(data);
            if (!Double.isNaN(actualKMO)) {
                double kmoGap = Math.abs(actualKMO - targetKMO);
                totalScore += Math.sqrt(kmoGap) * 30; // 适中权重
            } else {
                totalScore += 100.0; // NaN惩罚
            }
            scoreComponents++;
        }

        return scoreComponents > 0 ? totalScore / scoreComponents : totalScore;
    }

    /**
     * 计算均值偏差得分
     */
    private double calculateMeanDeviationScore(List<List<Double>> data, List<List<Integer>> dimensions,
                                             List<Integer> allColumns, List<List<Double>> targetItemMeans) {
        if (targetItemMeans == null || targetItemMeans.isEmpty()) {
            return 0.0;
        }

        double totalMeanScore = 0.0;
        int meanComponents = 0;

        for (int dimIdx = 0; dimIdx < dimensions.size(); dimIdx++) {
            List<Integer> dimension = dimensions.get(dimIdx);
            List<Double> dimTargetMeans = targetItemMeans.get(dimIdx);

            for (int itemIdx = 0; itemIdx < dimension.size(); itemIdx++) {
                Integer col = dimension.get(itemIdx);
                Double targetMean = dimTargetMeans.get(itemIdx);

                if (targetMean != null) {
                    int dataIdx = getDataIndex(col, allColumns);
                    double actualMean = data.get(dataIdx).stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
                    double meanGap = Math.abs(actualMean - targetMean);

                    // 均值偏差使用平方根，减少极端惩罚
                    totalMeanScore += Math.sqrt(meanGap) * 10; // 适中权重
                    meanComponents++;
                }
            }
        }

        return meanComponents > 0 ? totalMeanScore / meanComponents : 0.0;
    }

    /**
     * 计算动态核心权重 - 核心指标越接近达标，均值权重越高
     */
    private double calculateDynamicCoreWeight(List<List<Double>> data, List<List<Integer>> dimensions,
                                            List<Integer> allColumns, List<Double> targetDimensionAlphas,
                                            Double targetTotalAlpha, Double targetKMO) {
        int achievedCoreTargets = 0;
        int totalCoreTargets = 0;
        double tolerance = 0.02; // 达标容差

        // 检查维度信度达标情况
        for (int dimIdx = 0; dimIdx < dimensions.size(); dimIdx++) {
            List<Integer> dimension = dimensions.get(dimIdx);
            double targetAlpha = targetDimensionAlphas.get(dimIdx);

            List<List<Double>> dimensionData = new ArrayList<>();
            for (Integer col : dimension) {
                int dataIdx = getDataIndex(col, allColumns);
                dimensionData.add(data.get(dataIdx));
            }

            double actualAlpha = calculateCronbachAlpha(dimensionData);
            if (Math.abs(actualAlpha - targetAlpha) <= tolerance) {
                achievedCoreTargets++;
            }
            totalCoreTargets++;
        }

        // 检查总信度达标情况
        if (targetTotalAlpha != null) {
            double actualTotalAlpha = calculateCronbachAlpha(data);
            if (Math.abs(actualTotalAlpha - targetTotalAlpha) <= tolerance) {
                achievedCoreTargets++;
            }
            totalCoreTargets++;
        }

        // 检查KMO达标情况
        if (targetKMO != null) {
            double actualKMO = calculateKMOFromAdjustedData(data);
            if (!Double.isNaN(actualKMO) && Math.abs(actualKMO - targetKMO) <= tolerance) {
                achievedCoreTargets++;
            }
            totalCoreTargets++;
        }

        // 动态权重计算
        double coreAchievementRate = totalCoreTargets > 0 ? (double) achievedCoreTargets / totalCoreTargets : 0.0;

        // 核心指标达标率越高，核心权重越低，均值权重越高
        double baseCoreWeight = 0.85; // 基础核心权重85%
        double minCoreWeight = 0.60;  // 最小核心权重60%

        double coreWeight = baseCoreWeight - (coreAchievementRate * (baseCoreWeight - minCoreWeight));

        log.debug("[动态权重] 核心指标达标率: {:.1f}%, 核心权重: {:.3f}", coreAchievementRate * 100, coreWeight);

        return coreWeight;
    }

    /**
     * 执行平衡调整 - 核心指标优先但兼顾均值
     */
    private List<List<Double>> performBalancedAdjustment(List<List<Double>> data,
                                                        List<List<Integer>> dimensions,
                                                        List<Double> targetDimensionAlphas,
                                                        Double targetTotalAlpha,
                                                        Double targetKMO,
                                                        List<List<Double>> targetItemMeans,
                                                        Double tolerance,
                                                        double adjustmentIntensity,
                                                        List<Integer> allColumns,
                                                        int iteration) {

        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        // 评估当前状态
        BalanceStatus status = evaluateCurrentBalance(result, dimensions, allColumns, targetDimensionAlphas,
                                                     targetTotalAlpha, targetKMO, targetItemMeans, tolerance);

        log.info("[智能平衡] 第{}次循环状态评估 - 核心达标率: {:.1f}%, 均值达标率: {:.1f}%",
                iteration, status.coreAchievementRate * 100, status.meanAchievementRate * 100);

        // 根据状态选择调整策略
        if (status.coreAchievementRate < 0.7) {
            // 核心指标达标率低，优先调整核心指标
            log.info("[智能平衡] 核心指标达标率低，优先强化核心指标");
            result = adjustCoreIndicatorsPriority(result, dimensions, targetDimensionAlphas, targetTotalAlpha,
                                                 targetKMO, tolerance, adjustmentIntensity, allColumns);
        } else if (status.coreAchievementRate >= 0.9 && status.meanAchievementRate < 0.6) {
            // 核心指标基本达标，均值偏差较大，平衡调整
            log.info("[智能平衡] 核心指标基本达标，开始平衡调整均值");
            result = adjustWithBalancedStrategy(result, dimensions, targetDimensionAlphas, targetTotalAlpha,
                                              targetKMO, targetItemMeans, tolerance, adjustmentIntensity, allColumns);
        } else {
            // 中等状态，使用智能协调策略
            log.info("[智能平衡] 使用智能协调策略");
            result = adjustWithCoordinatedStrategy(result, dimensions, targetDimensionAlphas, targetTotalAlpha,
                                                  targetKMO, targetItemMeans, tolerance, adjustmentIntensity, allColumns);
        }

        // 确保数据变异性
        result = ensureDataVariabilityOptimized(result);

        return result;
    }

    /**
     * 平衡状态类
     */
    private static class BalanceStatus {
        double coreAchievementRate;
        double meanAchievementRate;
        int achievedCoreTargets;
        int totalCoreTargets;
        int achievedMeanTargets;
        int totalMeanTargets;
    }

    /**
     * 评估当前平衡状态
     */
    private BalanceStatus evaluateCurrentBalance(List<List<Double>> data,
                                               List<List<Integer>> dimensions,
                                               List<Integer> allColumns,
                                               List<Double> targetDimensionAlphas,
                                               Double targetTotalAlpha,
                                               Double targetKMO,
                                               List<List<Double>> targetItemMeans,
                                               Double tolerance) {
        BalanceStatus status = new BalanceStatus();

        // 评估核心指标
        int achievedCore = 0;
        int totalCore = 0;

        // 检查维度信度
        for (int dimIdx = 0; dimIdx < dimensions.size(); dimIdx++) {
            List<Integer> dimension = dimensions.get(dimIdx);
            double targetAlpha = targetDimensionAlphas.get(dimIdx);

            List<List<Double>> dimensionData = new ArrayList<>();
            for (Integer col : dimension) {
                int dataIdx = getDataIndex(col, allColumns);
                dimensionData.add(data.get(dataIdx));
            }

            double actualAlpha = calculateCronbachAlpha(dimensionData);
            if (Math.abs(actualAlpha - targetAlpha) <= tolerance) {
                achievedCore++;
            }
            totalCore++;
        }

        // 检查总信度
        if (targetTotalAlpha != null) {
            double actualTotalAlpha = calculateCronbachAlpha(data);
            if (Math.abs(actualTotalAlpha - targetTotalAlpha) <= tolerance) {
                achievedCore++;
            }
            totalCore++;
        }

        // 检查KMO
        if (targetKMO != null) {
            double actualKMO = calculateKMOFromAdjustedData(data);
            if (!Double.isNaN(actualKMO) && Math.abs(actualKMO - targetKMO) <= tolerance) {
                achievedCore++;
            }
            totalCore++;
        }

        status.achievedCoreTargets = achievedCore;
        status.totalCoreTargets = totalCore;
        status.coreAchievementRate = totalCore > 0 ? (double) achievedCore / totalCore : 0.0;

        // 评估均值指标
        int achievedMean = 0;
        int totalMean = 0;
        double meanTolerance = 0.08; // 均值容差放宽到0.08

        if (targetItemMeans != null && !targetItemMeans.isEmpty()) {
            for (int dimIdx = 0; dimIdx < dimensions.size(); dimIdx++) {
                List<Integer> dimension = dimensions.get(dimIdx);
                List<Double> dimTargetMeans = targetItemMeans.get(dimIdx);

                for (int itemIdx = 0; itemIdx < dimension.size(); itemIdx++) {
                    Integer col = dimension.get(itemIdx);
                    Double targetMean = dimTargetMeans.get(itemIdx);

                    if (targetMean != null) {
                        int dataIdx = getDataIndex(col, allColumns);
                        double actualMean = data.get(dataIdx).stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
                        if (Math.abs(actualMean - targetMean) <= meanTolerance) {
                            achievedMean++;
                        }
                        totalMean++;
                    }
                }
            }
        }

        status.achievedMeanTargets = achievedMean;
        status.totalMeanTargets = totalMean;
        status.meanAchievementRate = totalMean > 0 ? (double) achievedMean / totalMean : 1.0;

        return status;
    }

    /**
     * 核心指标优先调整策略
     */
    private List<List<Double>> adjustCoreIndicatorsPriority(List<List<Double>> data,
                                                          List<List<Integer>> dimensions,
                                                          List<Double> targetDimensionAlphas,
                                                          Double targetTotalAlpha,
                                                          Double targetKMO,
                                                          Double tolerance,
                                                          double adjustmentIntensity,
                                                          List<Integer> allColumns) {
        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        // 使用高强度调整核心指标
        double coreIntensity = Math.min(1.0, adjustmentIntensity * 1.8);

        // 调整维度信度
        result = adjustDimensionAlphasIntelligently(result, dimensions, targetDimensionAlphas, tolerance, coreIntensity);

        // 调整总信度
        if (targetTotalAlpha != null) {
            result = adjustTotalAlphaIntelligentlyOptimized(result, targetTotalAlpha, tolerance, coreIntensity);
        }

        // 调整KMO
        if (targetKMO != null) {
            result = adjustKMOIntelligentlyOptimized(result, targetKMO, tolerance, coreIntensity);
        }

        return result;
    }

    /**
     * 平衡调整策略
     */
    private List<List<Double>> adjustWithBalancedStrategy(List<List<Double>> data,
                                                        List<List<Integer>> dimensions,
                                                        List<Double> targetDimensionAlphas,
                                                        Double targetTotalAlpha,
                                                        Double targetKMO,
                                                        List<List<Double>> targetItemMeans,
                                                        Double tolerance,
                                                        double adjustmentIntensity,
                                                        List<Integer> allColumns) {
        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        // 使用中等强度，平衡调整
        double balancedIntensity = adjustmentIntensity * 0.8;

        // 轻度调整核心指标以保持达标
        result = adjustDimensionAlphasIntelligently(result, dimensions, targetDimensionAlphas, tolerance, balancedIntensity * 0.6);

        if (targetTotalAlpha != null) {
            result = adjustTotalAlphaIntelligentlyOptimized(result, targetTotalAlpha, tolerance, balancedIntensity * 0.6);
        }

        if (targetKMO != null) {
            result = adjustKMOIntelligentlyOptimized(result, targetKMO, tolerance, balancedIntensity * 0.6);
        }

        // 适度调整均值
        if (targetItemMeans != null && !targetItemMeans.isEmpty()) {
            result = adjustItemMeansWithModeration(result, dimensions, targetItemMeans, allColumns, balancedIntensity);
        }

        return result;
    }

    /**
     * 智能协调策略
     */
    private List<List<Double>> adjustWithCoordinatedStrategy(List<List<Double>> data,
                                                           List<List<Integer>> dimensions,
                                                           List<Double> targetDimensionAlphas,
                                                           Double targetTotalAlpha,
                                                           Double targetKMO,
                                                           List<List<Double>> targetItemMeans,
                                                           Double tolerance,
                                                           double adjustmentIntensity,
                                                           List<Integer> allColumns) {
        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        // 协调强度
        double coordIntensity = adjustmentIntensity * 1.0;

        // 同时调整核心指标和均值，但权重不同
        result = adjustDimensionAlphasIntelligently(result, dimensions, targetDimensionAlphas, tolerance, coordIntensity);

        if (targetTotalAlpha != null) {
            result = adjustTotalAlphaIntelligentlyOptimized(result, targetTotalAlpha, tolerance, coordIntensity);
        }

        if (targetKMO != null) {
            result = adjustKMOIntelligentlyOptimized(result, targetKMO, tolerance, coordIntensity);
        }

        // 协调调整均值
        if (targetItemMeans != null && !targetItemMeans.isEmpty()) {
            result = adjustItemMeansWithModeration(result, dimensions, targetItemMeans, allColumns, coordIntensity * 0.7);
        }

        return result;
    }

    /**
     * 适度均值调整
     */
    private List<List<Double>> adjustItemMeansWithModeration(List<List<Double>> data,
                                                           List<List<Integer>> dimensions,
                                                           List<List<Double>> targetItemMeans,
                                                           List<Integer> allColumns,
                                                           double adjustmentIntensity) {
        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        // 适度调整均值，避免过度偏离
        for (int dimIdx = 0; dimIdx < dimensions.size(); dimIdx++) {
            List<Integer> dimension = dimensions.get(dimIdx);
            List<Double> dimTargetMeans = targetItemMeans.get(dimIdx);

            for (int itemIdx = 0; itemIdx < dimension.size(); itemIdx++) {
                Integer col = dimension.get(itemIdx);
                Double targetMean = dimTargetMeans.get(itemIdx);
                int dataIdx = getDataIndex(col, allColumns);

                if (targetMean == null) {
                    continue;
                }

                double currentMean = result.get(dataIdx).stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
                double deviation = Math.abs(currentMean - targetMean);

                // 只有偏差较大时才调整（0.06以上）
                if (deviation > 0.06) {
                    List<Double> moderateAdjusted = adjustSingleItemMeanWithIntensity(
                            result.get(dataIdx), targetMean, adjustmentIntensity * 0.6); // 使用适度强度
                    result.set(dataIdx, moderateAdjusted);

                    log.debug("[适度均值调整] 题目{}均值适度调整: {:.3f} -> {:.3f}, 目标: {:.3f}",
                             col, currentMean,
                             moderateAdjusted.stream().mapToDouble(Double::doubleValue).average().orElse(0.0),
                             targetMean);
                }
            }
        }

        return result;
    }
 
    /**
     * 记录当前状态（包含均值信息）- 增强版本
     */
    private void logCurrentStatusWithMeanInfo(List<List<Double>> data, List<List<Integer>> dimensions,
                                            List<Integer> allColumns, List<Double> targetDimensionAlphas,
                                            Double targetTotalAlpha, Double targetKMO,
                                            List<List<Double>> targetItemMeans, int iteration) {

        log.info("[状态记录] 第{}次智能循环后的状态（含均值信息）:", iteration);

        int achievedCount = 0;
        int totalCount = 0;
        double maxMeanDeviation = 0.0;

        // 记录各维度信度（超精确版）
        log.info("[状态记录] === 维度信度超精确分析 ===");
        for (int dimIdx = 0; dimIdx < dimensions.size(); dimIdx++) {
            List<Integer> dimension = dimensions.get(dimIdx);
            double targetAlpha = targetDimensionAlphas.get(dimIdx);

            List<List<Double>> dimensionData = new ArrayList<>();
            for (Integer col : dimension) {
                int dataIdx = getDataIndex(col, allColumns);
                dimensionData.add(data.get(dataIdx));
            }

            double actualAlpha = calculateCronbachAlpha(dimensionData);
            double deviation = Math.abs(actualAlpha - targetAlpha);

            // 超精确状态分类
            String status;
            String priority = "";
            if (deviation <= 0.003) {
                status = "✓超高精度";
                achievedCount++;
            } else if (deviation <= 0.008) {
                status = "✓高精度";
                achievedCount++;
            } else if (deviation <= 0.015) {
                status = "✓达标";
                achievedCount++;
            } else if (deviation <= 0.03) {
                status = "接近目标";
                priority = " [优先调整]";
            } else {
                status = "需重点调整";
                priority = " [高优先级]";
            }
            totalCount++;

            // 计算题目间平均相关性
            double avgCorrelation = calculateAverageInterItemCorrelation(dimensionData);

            log.info("[状态记录] 🎯维度{}信度: 实际{:.4f}, 目标{:.4f}, 偏差{:.4f} - {} (题目数:{}, 平均相关性:{:.3f}){} [核心指标]",
                    dimIdx + 1, actualAlpha, targetAlpha, deviation, status, dimension.size(), avgCorrelation, priority);
        }

        // 记录总信度
        if (targetTotalAlpha != null) {
            double actualTotalAlpha = calculateCronbachAlpha(data);
            boolean achieved = Math.abs(actualTotalAlpha - targetTotalAlpha) <= 0.02;
            String status = achieved ? "✓达标" : "需优化";
            if (achieved) achievedCount++;
            totalCount++;

            log.info("[状态记录] 🎯总信度: 实际{:.4f}, 目标{:.4f}, 差距{:.4f} - {} [核心指标]",
                    actualTotalAlpha, targetTotalAlpha, Math.abs(actualTotalAlpha - targetTotalAlpha), status);
        }

        // 记录KMO
        if (targetKMO != null) {
            double actualKMO = calculateKMOFromAdjustedData(data);
            boolean achieved = (!Double.isNaN(actualKMO) && Math.abs(actualKMO - targetKMO) <= 0.02);
            String status = achieved ? "✓达标" : "需优化";
            if (achieved) achievedCount++;
            totalCount++;

            log.info("[状态记录] 🎯KMO: 实际{:.4f}, 目标{:.4f}, 差距{:.4f} - {} [核心指标]",
                    actualKMO, targetKMO, Double.isNaN(actualKMO) ? Double.NaN : Math.abs(actualKMO - targetKMO), status);
        }

        // 记录均值偏差信息（次要指标）
        if (targetItemMeans != null && !targetItemMeans.isEmpty()) {
            log.info("[状态记录] === 题目均值偏差情况（次要指标）===");
            int meanAchievedCount = 0;
            int meanTotalCount = 0;

            for (int dimIdx = 0; dimIdx < dimensions.size(); dimIdx++) {
                List<Integer> dimension = dimensions.get(dimIdx);
                List<Double> dimTargetMeans = targetItemMeans.get(dimIdx);

                for (int itemIdx = 0; itemIdx < dimension.size(); itemIdx++) {
                    Integer col = dimension.get(itemIdx);
                    Double targetMean = dimTargetMeans.get(itemIdx);

                    if (targetMean != null) {
                        int dataIdx = getDataIndex(col, allColumns);
                        double actualMean = data.get(dataIdx).stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
                        double deviation = Math.abs(actualMean - targetMean);

                        maxMeanDeviation = Math.max(maxMeanDeviation, deviation);

                        boolean meanAchieved = deviation <= 0.05; // 放宽均值标准，优先保证信度
                        String meanStatus = meanAchieved ? "✓可接受" : (deviation <= 0.1 ? "尚可" : "偏差较大");
                        if (meanAchieved) meanAchievedCount++;
                        meanTotalCount++;

                        log.info("[状态记录] 📊题目{}均值: 实际{:.3f}, 目标{:.3f}, 偏差{:.3f} - {} [次要指标]",
                                col, actualMean, targetMean, deviation, meanStatus);
                    }
                }
            }

            log.info("[状态记录] 📊均值达成率: {}/{} ({:.1f}%), 最大偏差: {:.3f} [次要指标，可适度偏差]",
                    meanAchievedCount, meanTotalCount,
                    meanTotalCount > 0 ? (double) meanAchievedCount / meanTotalCount * 100 : 0,
                    maxMeanDeviation);
        }

        log.info("[状态记录] 总体进度: {}/{} ({:.1f}%)", achievedCount, totalCount,
                (double) achievedCount / totalCount * 100);
    }

    /**
     * 计算题目间平均相关性
     */
    private double calculateAverageInterItemCorrelation(List<List<Double>> dimensionData) {
        if (dimensionData.size() < 2) {
            return 0.0;
        }

        double totalCorrelation = 0.0;
        int correlationCount = 0;

        for (int i = 0; i < dimensionData.size() - 1; i++) {
            for (int j = i + 1; j < dimensionData.size(); j++) {
                double correlation = calculateCorrelation(dimensionData.get(i), dimensionData.get(j));
                if (!Double.isNaN(correlation)) {
                    totalCorrelation += correlation;
                    correlationCount++;
                }
            }
        }

        return correlationCount > 0 ? totalCorrelation / correlationCount : 0.0;
    }

    /**
     * 优化版本的数据变异性保证 - 更智能的变异性控制
     */
    private List<List<Double>> ensureDataVariabilityOptimized(List<List<Double>> data) {
        List<List<Double>> result = new ArrayList<>();
        boolean hasVariabilityIssues = false;

        log.debug("[变异性保证] 开始智能变异性检查");

        for (int i = 0; i < data.size(); i++) {
            List<Double> col = data.get(i);
            List<Double> adjustedCol = new ArrayList<>();

            // 更严格的变异性检查
            if (needsVariabilityImprovement(col)) {
                log.debug("[变异性保证] 第{}列需要改进变异性", i + 1);
                adjustedCol = improveVariabilityIntelligently(col);
                hasVariabilityIssues = true;
            } else {
                adjustedCol = new ArrayList<>(col);
            }

            result.add(adjustedCol);
        }

        // 检查列间相关性，防止完全相同的列
        if (hasHighlyCorrelatedColumns(result)) {
            log.debug("[变异性保证] 检测到高度相关的列，进行差异化处理");
            result = differentiateHighlyCorrelatedColumns(result);
            hasVariabilityIssues = true;
        }

        if (hasVariabilityIssues) {
            log.debug("[变异性保证] 智能变异性修复完成");
        }

        return result;
    }

    /**
     * 检查列是否需要改进变异性
     */
    private boolean needsVariabilityImprovement(List<Double> col) {
        if (col == null || col.isEmpty()) {
            return true;
        }

        // 检查是否所有值都相同
        if (isAllValuesSame(col)) {
            return true;
        }

        // 计算统计指标
        DescriptiveStatistics stats = new DescriptiveStatistics();
        col.forEach(value -> {
            if (value != null && !Double.isNaN(value) && Double.isFinite(value)) {
                stats.addValue(value);
            }
        });

        double variance = stats.getVariance();
        double stdDev = stats.getStandardDeviation();

        // 更严格的变异性要求
        if (variance < 0.01 || stdDev < 0.1) {
            return true;
        }

        // 检查唯一值的数量
        Set<Double> uniqueValues = new HashSet<>();
        for (Double value : col) {
            if (value != null && !Double.isNaN(value) && Double.isFinite(value)) {
                uniqueValues.add(Math.round(value * 10.0) / 10.0); // 保留1位小数
            }
        }

        return uniqueValues.size() < 3; // 至少需要3个不同的值
    }

    /**
     * 智能改进变异性
     */
    private List<Double> improveVariabilityIntelligently(List<Double> col) {
        if (col.isEmpty()) {
            return new ArrayList<>(col);
        }

        // 获取基准值
        Double baseValue = col.stream()
                .filter(v -> v != null && !Double.isNaN(v) && Double.isFinite(v))
                .findFirst()
                .orElse(3.0); // 默认值

        List<Double> result = new ArrayList<>();
        Random random = new Random(42); // 使用固定种子确保可重现性

        // 创建更自然的变异分布
        for (int i = 0; i < col.size(); i++) {
            Double originalValue = col.get(i);

            if (originalValue != null && !Double.isNaN(originalValue) && Double.isFinite(originalValue)) {
                // 基于正态分布添加变异
                double noise = random.nextGaussian() * 0.3; // 标准差为0.3
                double newValue = baseValue + noise;

                // 确保在量表范围内
                newValue = Math.max(1.0, Math.min((double)currentScaleLevel, newValue));
                result.add(newValue);
            } else {
                result.add(originalValue);
            }
        }

        return result;
    }

    /**
     * 检查是否存在高度相关的列
     */
    private boolean hasHighlyCorrelatedColumns(List<List<Double>> data) {
        if (data.size() < 2) {
            return false;
        }

        for (int i = 0; i < data.size() - 1; i++) {
            for (int j = i + 1; j < data.size(); j++) {
                double correlation = calculateCorrelation(data.get(i), data.get(j));
                if (Math.abs(correlation) > 0.95) { // 相关系数超过0.95认为过高
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 差异化高度相关的列
     */
    private List<List<Double>> differentiateHighlyCorrelatedColumns(List<List<Double>> data) {
        List<List<Double>> result = new ArrayList<>();
        Random random = new Random(42);

        for (int i = 0; i < data.size(); i++) {
            List<Double> col = new ArrayList<>(data.get(i));

            // 为每一列添加独特的微小变异
            for (int j = 0; j < col.size(); j++) {
                if (col.get(j) != null && !Double.isNaN(col.get(j)) && Double.isFinite(col.get(j))) {
                    // 基于列索引添加独特的变异模式
                    double uniqueVariation = Math.sin(i * 0.5) * 0.1 + random.nextGaussian() * 0.05;
                    double newValue = col.get(j) + uniqueVariation;
                    newValue = Math.max(1.0, Math.min((double)currentScaleLevel, newValue));
                    col.set(j, newValue);
                }
            }

            result.add(col);
        }

        return result;
    }

    /**
     * 检查数据有效性 - 重点检查同值问题和KMO计算所需的条件
     */
    private boolean isDataValid(List<List<Double>> data) {
        if (data == null || data.isEmpty()) {
            log.warn("[数据有效性检查] 数据为空");
            return false;
        }

        for (int i = 0; i < data.size(); i++) {
            List<Double> col = data.get(i);
            if (col == null || col.isEmpty()) {
                log.warn("[数据有效性检查] 第{}列数据为空", i + 1);
                return false;
            }

            // 检查是否有有效数据
            List<Double> validValues = new ArrayList<>();
            for (Double value : col) {
                if (value != null && !Double.isNaN(value) && Double.isFinite(value)) {
                    validValues.add(value);
                }
            }

            if (validValues.isEmpty()) {
                log.warn("[数据有效性检查] 第{}列没有有效数据", i + 1);
                return false;
            }

            // 关键检查：是否所有值都相同（这是KMO为NaN的主要原因）
            if (isAllValuesSame(validValues)) {
                log.error("[数据有效性检查] 第{}列所有值都相同({})，无法进行统计分析", i + 1, validValues.get(0));
                return false;
            }

            // 检查方差是否过小（提高阈值，确保KMO计算稳定）
            DescriptiveStatistics stats = new DescriptiveStatistics();
            validValues.forEach(stats::addValue);
            double variance = stats.getVariance();
            double stdDev = stats.getStandardDeviation();

            if (variance < 1e-6 || stdDev < 1e-3) {
                log.error("[数据有效性检查] 第{}列方差过小(方差:{}, 标准差:{})，无法支持KMO计算", i + 1, variance, stdDev);
                return false;
            }

            // 检查数据分布是否过于集中（至少需要2个不同的值）
            Set<Double> uniqueValues = new HashSet<>(validValues);
            if (uniqueValues.size() < 2) {
                log.error("[数据有效性检查] 第{}列只有{}个不同的值，无法进行相关分析", i + 1, uniqueValues.size());
                return false;
            }
        }

        // 额外检查：确保没有完全相同的列（这也会导致KMO计算问题）
        if (hasIdenticalColumns(data)) {
            log.error("[数据有效性检查] 存在完全相同的列，无法进行因子分析");
            return false;
        }

        return true;
    }

    /**
     * 检查是否所有值都相同
     */
    private boolean isAllValuesSame(List<Double> values) {
        if (values.size() <= 1) {
            return true;
        }

        Double firstValue = values.get(0);
        for (int i = 1; i < values.size(); i++) {
            if (Math.abs(values.get(i) - firstValue) > 1e-10) {
                return false;
            }
        }
        return true;
    }

    /**
     * 检查是否有完全相同的列（这会导致KMO计算异常）
     */
    private boolean hasIdenticalColumns(List<List<Double>> data) {
        if (data == null || data.size() < 2) {
            return false;
        }

        for (int i = 0; i < data.size(); i++) {
            for (int j = i + 1; j < data.size(); j++) {
                if (areColumnsIdentical(data.get(i), data.get(j))) {
                    log.warn("[同值检查] 第{}列和第{}列完全相同", i + 1, j + 1);
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 检查两列是否完全相同
     */
    private boolean areColumnsIdentical(List<Double> col1, List<Double> col2) {
        if (col1.size() != col2.size()) {
            return false;
        }

        for (int i = 0; i < col1.size(); i++) {
            Double val1 = col1.get(i);
            Double val2 = col2.get(i);

            // 处理null值
            if (val1 == null && val2 == null) {
                continue;
            }
            if (val1 == null || val2 == null) {
                return false;
            }

            // 检查数值差异（考虑浮点精度）
            if (Math.abs(val1 - val2) > 1e-10) {
                return false;
            }
        }

        return true;
    }

    /**
     * 为数据添加最小变异性，防止同值问题 - 增强版本
     * 这是防止KMO为NaN的关键方法，确保数据满足统计分析的基本要求
     */
    private List<List<Double>> ensureDataVariability(List<List<Double>> data) {
        List<List<Double>> result = new ArrayList<>();
        boolean hasVariabilityIssues = false;

        log.info("[变异性保证] 开始检查和修复数据变异性问题");

        for (int i = 0; i < data.size(); i++) {
            List<Double> col = data.get(i);
            List<Double> adjustedCol = new ArrayList<>();

            // 检查是否所有值都相同
            if (isAllValuesSame(col)) {
                log.warn("[变异性保证] 第{}列所有值都相同，添加变异性", i + 1);
                adjustedCol = addMinimalVariability(col);
                hasVariabilityIssues = true;
            } else {
                // 检查方差是否过小（提高阈值以确保KMO计算稳定）
                DescriptiveStatistics stats = new DescriptiveStatistics();
                col.forEach(value -> {
                    if (value != null && !Double.isNaN(value) && Double.isFinite(value)) {
                        stats.addValue(value);
                    }
                });

                double variance = stats.getVariance();
                double stdDev = stats.getStandardDeviation();

                // 更严格的变异性检查 - 防止KMO为NaN的关键
                if (variance < 1e-3 || stdDev < 0.05) {
                    log.warn("[变异性保证] 第{}列方差过小(方差:{:.6f}, 标准差:{:.6f})，添加变异性", i + 1, variance, stdDev);
                    adjustedCol = addMinimalVariability(col);
                    hasVariabilityIssues = true;
                } else {
                    // 检查唯一值的数量 - 更严格的要求
                    Set<Double> uniqueValues = new HashSet<>();
                    for (Double value : col) {
                        if (value != null && !Double.isNaN(value) && Double.isFinite(value)) {
                            uniqueValues.add(Math.round(value * 100.0) / 100.0); // 保留2位小数进行比较，更严格
                        }
                    }

                    // 要求至少有5个不同的值，确保足够的变异性
                    if (uniqueValues.size() < 5) {
                        log.warn("[变异性保证] 第{}列只有{}个不同的值，增加变异性", i + 1, uniqueValues.size());
                        adjustedCol = addMinimalVariability(col);
                        hasVariabilityIssues = true;
                    } else {
                        adjustedCol = new ArrayList<>(col);
                    }
                }
            }

            result.add(adjustedCol);
        }

        // 检查是否有完全相同的列
        if (hasIdenticalColumns(result)) {
            log.warn("[变异性保证] 检测到完全相同的列，进行差异化处理");
            result = differentiateIdenticalColumns(result);
            hasVariabilityIssues = true;
        }

        if (hasVariabilityIssues) {
            log.info("[变异性保证] 变异性修复完成，重新验证数据质量");
            // 最终验证
            if (!isDataValid(result)) {
                log.error("[变异性保证] 修复后数据仍然无效，可能需要更激进的修复策略");
            }
        } else {
            log.info("[变异性保证] 数据变异性检查通过，无需修复");
        }

        return result;
    }

    /**
     * 差异化完全相同的列
     */
    private List<List<Double>> differentiateIdenticalColumns(List<List<Double>> data) {
        List<List<Double>> result = new ArrayList<>();

        for (int i = 0; i < data.size(); i++) {
            List<Double> col = new ArrayList<>(data.get(i));

            // 为每一列添加微小的、独特的变异
            for (int j = 0; j < col.size(); j++) {
                if (col.get(j) != null && !Double.isNaN(col.get(j)) && Double.isFinite(col.get(j))) {
                    // 基于列索引和行索引添加独特的微小变异
                    double uniqueVariation = (i * 0.001 + j * 0.0001) % 0.01;
                    double newValue = col.get(j) + uniqueVariation;
                    // 根据当前设置的量表级数并限制范围
                    newValue = Math.max(1.0, Math.min((double)currentScaleLevel, newValue));
                    col.set(j, newValue);
                }
            }

            result.add(col);
        }

        return result;
    }

    /**
     * 为同值数据添加最小变异性 - 增强版本
     * 确保产生足够的变异性以支持KMO计算
     */
    private List<Double> addMinimalVariability(List<Double> col) {
        List<Double> result = new ArrayList<>();

        if (col.isEmpty()) {
            return result;
        }

        // 获取基准值
        Double baseValue = null;
        for (Double value : col) {
            if (value != null && !Double.isNaN(value) && Double.isFinite(value)) {
                baseValue = value;
                break;
            }
        }

        if (baseValue == null) {
            return new ArrayList<>(col);
        }

        log.info("[变异性增强] 为同值数据添加变异性，基准值: {}", baseValue);

        // 创建更有意义的变异性分布
        List<Double> variationPattern = createVariationPattern(col.size(), baseValue);

        for (int i = 0; i < col.size(); i++) {
            Double originalValue = col.get(i);

            if (originalValue != null && !Double.isNaN(originalValue) && Double.isFinite(originalValue)) {
                // 使用预定义的变异模式，确保有足够的分散性
                double newValue = variationPattern.get(i);

                // 根据当前设置的量表级数并确保在合理范围内
                newValue = Math.max(1.0, Math.min((double)currentScaleLevel, newValue));
                result.add(newValue);
            } else {
                result.add(originalValue);
            }
        }

        // 验证变异性是否足够
        DescriptiveStatistics stats = new DescriptiveStatistics();
        result.forEach(value -> {
            if (value != null && !Double.isNaN(value) && Double.isFinite(value)) {
                stats.addValue(value);
            }
        });

        log.info("[变异性增强] 调整后方差: {:.6f}, 标准差: {:.6f}", stats.getVariance(), stats.getStandardDeviation());

        return result;
    }

    /**
     * 创建有意义的变异模式，确保数据有足够的分散性 - 防止KMO为NaN的关键
     */
    private List<Double> createVariationPattern(int size, double baseValue) {
        List<Double> pattern = new ArrayList<>();
        Random random = new Random();

        // 使用当前设置的量表级数
        int scaleLevel = currentScaleLevel != null ? currentScaleLevel : 5;

        // 创建至少5个不同的目标值，确保足够的变异性
        double[] targetValues = new double[5];
        double range = scaleLevel - 1.0; // 量表范围

        // 在量表范围内均匀分布5个目标值
        for (int i = 0; i < 5; i++) {
            targetValues[i] = 1.0 + (range * i / 4.0);
        }

        // 如果基准值接近边界，调整分布策略
        double lowerBoundary = 1.5;
        double upperBoundary = scaleLevel - 0.5;

        if (baseValue <= lowerBoundary) {
            // 基准值较低，向上分布
            for (int i = 0; i < 5; i++) {
                targetValues[i] = 1.0 + (i * 0.5);
                targetValues[i] = Math.min(targetValues[i], (double)scaleLevel);
            }
        } else if (baseValue >= upperBoundary) {
            // 基准值较高，向下分布
            for (int i = 0; i < 5; i++) {
                targetValues[i] = scaleLevel - (4 - i) * 0.5;
                targetValues[i] = Math.max(targetValues[i], 1.0);
            }
        }

        // 按比例分配不同的值，确保每个目标值都有数据点
        for (int i = 0; i < size; i++) {
            int targetIndex = i % 5; // 循环使用5个目标值
            double targetValue = targetValues[targetIndex];

            // 添加小幅随机变异，但保持在目标值附近
            double noise = random.nextGaussian() * 0.15;
            targetValue += noise;

            // 确保在有效范围内
            targetValue = Math.max(1.0, Math.min((double)scaleLevel, targetValue));
            pattern.add(targetValue);
        }

        // 打乱顺序，避免规律性
        java.util.Collections.shuffle(pattern);

        // 验证创建的模式确实有足够的变异性
        Set<Double> uniqueValues = new HashSet<>();
        for (Double value : pattern) {
            uniqueValues.add(Math.round(value * 100.0) / 100.0);
        }

        log.info("[变异性创建] 创建了{}个数据点，包含{}个不同的值", size, uniqueValues.size());

        return pattern;
    }


   
    /**
     * 超精确调整题目均值 - 全新算法
     * 使用数学优化方法确保均值精度达到0.001以内
     */
    private List<List<Double>> adjustItemMeansPreciselyOptimized(List<List<Double>> data,
                                                               List<List<Integer>> dimensions,
                                                               List<List<Double>> targetItemMeans) {
        log.info("[超精确均值调整] 开始超精确均值调整，目标精度: 0.001");

        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        List<Integer> allColumns = getAllColumns(dimensions);

        // 第一轮：粗调整 - 快速接近目标
        for (int dimIdx = 0; dimIdx < dimensions.size(); dimIdx++) {
            List<Integer> dimension = dimensions.get(dimIdx);
            List<Double> dimTargetMeans = targetItemMeans.get(dimIdx);

            for (int itemIdx = 0; itemIdx < dimension.size(); itemIdx++) {
                Integer col = dimension.get(itemIdx);
                Double targetMean = dimTargetMeans.get(itemIdx);
                int dataIdx = getDataIndex(col, allColumns);

                if (targetMean == null) {
                    continue;
                }

                log.debug("[超精确均值调整] 粗调整维度{}题目{}，目标均值: {}", dimIdx + 1, col, targetMean);

                // 获取该题目的量表级数
                Integer scaleLevel = getQuestionScaleLevel(col, result.get(dataIdx));

                List<Double> roughAdjusted = adjustSingleItemMeanRoughly(result.get(dataIdx), targetMean, scaleLevel);
                result.set(dataIdx, roughAdjusted);
            }
        }

        // 第二轮：精调整 - 数学优化方法
        for (int dimIdx = 0; dimIdx < dimensions.size(); dimIdx++) {
            List<Integer> dimension = dimensions.get(dimIdx);
            List<Double> dimTargetMeans = targetItemMeans.get(dimIdx);

            for (int itemIdx = 0; itemIdx < dimension.size(); itemIdx++) {
                Integer col = dimension.get(itemIdx);
                Double targetMean = dimTargetMeans.get(itemIdx);
                int dataIdx = getDataIndex(col, allColumns);

                if (targetMean == null) {
                    continue;
                }

                log.debug("[超精确均值调整] 精调整维度{}题目{}，目标均值: {}", dimIdx + 1, col, targetMean);

                // 获取该题目的量表级数
                Integer scaleLevel = getQuestionScaleLevel(col, result.get(dataIdx));

                List<Double> preciseAdjusted = adjustSingleItemMeanUltraPrecisely(result.get(dataIdx), targetMean, scaleLevel);
                result.set(dataIdx, preciseAdjusted);

                // 验证调整结果
                double actualMean = preciseAdjusted.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
                double deviation = Math.abs(actualMean - targetMean);
                log.info("[超精确均值调整] 题目{}调整完成，实际均值: {:.4f}, 目标均值: {:.4f}, 偏差: {:.4f}（{}级量表）",
                        col, actualMean, targetMean, deviation, scaleLevel);

                if (deviation > 0.005) {
                    log.warn("[超精确均值调整] 题目{}均值偏差较大: {:.4f}", col, deviation);
                }
            }
        }

        // 第三轮：全局微调 - 确保所有均值同时达标
        result = performGlobalMeanFineTuning(result, dimensions, targetItemMeans, allColumns);

        return result;
    }

    /**
     * 粗调整单个题目均值 - 快速接近目标
     */
    private List<Double> adjustSingleItemMeanRoughly(List<Double> originalData, Double targetMean, Integer scaleLevel) {
        if (targetMean == null) {
            return new ArrayList<>(originalData);
        }

        if (scaleLevel == null) {
            scaleLevel = currentScaleLevel;
        }

        List<Double> result = new ArrayList<>(originalData);
        double currentMean = result.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
        double gap = targetMean - currentMean;

        if (Math.abs(gap) < 0.01) {
            return result; // 已经足够接近
        }

        // 使用线性变换快速调整
        double scaleFactor = targetMean / currentMean;

        // 如果比例过于极端，使用加法调整
        if (scaleFactor < 0.5 || scaleFactor > 2.0) {
            // 使用加法调整
            for (int i = 0; i < result.size(); i++) {
                double newValue = result.get(i) + gap;
                newValue = Math.max(1.0, Math.min((double)scaleLevel, newValue));
                result.set(i, newValue);
            }
        } else {
            // 使用乘法调整
            for (int i = 0; i < result.size(); i++) {
                double newValue = result.get(i) * scaleFactor;
                newValue = Math.max(1.0, Math.min((double)scaleLevel, newValue));
                result.set(i, newValue);
            }
        }

        return result;
    }

    /**
     * 超精确调整单个题目均值 - 数学优化方法
     */
    private List<Double> adjustSingleItemMeanUltraPrecisely(List<Double> originalData, Double targetMean, Integer scaleLevel) {
        if (targetMean == null) {
            return new ArrayList<>(originalData);
        }

        if (scaleLevel == null) {
            scaleLevel = currentScaleLevel;
        }

        List<Double> result = new ArrayList<>(originalData);
        double currentMean = result.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);

        if (Math.abs(currentMean - targetMean) < 0.001) {
            return result; // 已经达到超高精度
        }

        log.debug("[超精确调整] 开始超精确调整，当前均值: {:.4f}, 目标均值: {:.4f}", currentMean, targetMean);

        // 使用数学优化方法：最小二乘法调整
        result = optimizeMeanWithLeastSquares(result, targetMean, scaleLevel);

        // 验证并进行微调
        for (int iteration = 0; iteration < 20; iteration++) {
            currentMean = result.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
            double gap = targetMean - currentMean;

            if (Math.abs(gap) < 0.001) {
                break; // 达到超高精度
            }

            // 精确微调：选择最优的调整点
            result = performPreciseMicroAdjustment(result, gap, scaleLevel);
        }

        double finalMean = result.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
        log.debug("[超精确调整] 调整完成，最终均值: {:.4f}, 目标均值: {:.4f}, 偏差: {:.4f}",
                 finalMean, targetMean, Math.abs(finalMean - targetMean));

        return result;
    }

    /**
     * 使用最小二乘法优化均值
     */
    private List<Double> optimizeMeanWithLeastSquares(List<Double> data, Double targetMean, Integer scaleLevel) {
        List<Double> result = new ArrayList<>(data);
        double currentMean = result.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
        double gap = targetMean - currentMean;

        if (Math.abs(gap) < 0.001) {
            return result;
        }

        // 计算每个数据点到边界的距离，用于权重计算
        List<Double> weights = new ArrayList<>();
        for (Double value : result) {
            // 距离边界越远，权重越大（更容易调整）
            double distanceToLower = value - 1.0;
            double distanceToUpper = scaleLevel - value;
            double weight = Math.min(distanceToLower, distanceToUpper);
            weights.add(Math.max(0.1, weight)); // 最小权重0.1
        }

        // 计算总权重
        double totalWeight = weights.stream().mapToDouble(Double::doubleValue).sum();

        // 按权重分配调整量
        for (int i = 0; i < result.size(); i++) {
            double weight = weights.get(i);
            double adjustment = gap * (weight / totalWeight) * result.size(); // 放大调整量
            double newValue = result.get(i) + adjustment;
            newValue = Math.max(1.0, Math.min((double)scaleLevel, newValue));
            result.set(i, newValue);
        }

        return result;
    }

    /**
     * 执行精确微调
     */
    private List<Double> performPreciseMicroAdjustment(List<Double> data, double gap, Integer scaleLevel) {
        List<Double> result = new ArrayList<>(data);

        if (Math.abs(gap) < 0.0001) {
            return result;
        }

        // 找到最适合调整的数据点
        List<Integer> adjustableIndices = findMostAdjustableIndices(result, gap > 0, scaleLevel);

        if (adjustableIndices.isEmpty()) {
            return result;
        }

        // 计算每个点的调整量
        double adjustmentPerPoint = gap / adjustableIndices.size();

        for (Integer idx : adjustableIndices) {
            double currentValue = result.get(idx);
            double newValue = currentValue + adjustmentPerPoint;
            newValue = Math.max(1.0, Math.min((double)scaleLevel, newValue));
            result.set(idx, newValue);
        }

        return result;
    }

    /**
     * 找到最适合调整的数据点索引
     */
    private List<Integer> findMostAdjustableIndices(List<Double> data, boolean increaseDirection, Integer scaleLevel) {
        List<Integer> indices = new ArrayList<>();

        // 创建值-索引-可调整性的三元组
        List<AdjustabilityInfo> adjustabilityInfos = new ArrayList<>();

        for (int i = 0; i < data.size(); i++) {
            double value = data.get(i);
            double adjustability;

            if (increaseDirection) {
                // 需要增加：距离上边界越远，可调整性越高
                adjustability = scaleLevel - value;
            } else {
                // 需要减少：距离下边界越远，可调整性越高
                adjustability = value - 1.0;
            }

            adjustabilityInfos.add(new AdjustabilityInfo(i, value, adjustability));
        }

        // 按可调整性排序
        adjustabilityInfos.sort((a, b) -> Double.compare(b.adjustability, a.adjustability));

        // 选择前50%最可调整的点，但至少选择1个，最多选择80%
        int minCount = 1;
        int maxCount = Math.max(1, (int)(data.size() * 0.8));
        int targetCount = Math.max(minCount, Math.min(maxCount, data.size() / 2));

        for (int i = 0; i < targetCount && i < adjustabilityInfos.size(); i++) {
            if (adjustabilityInfos.get(i).adjustability > 0.1) { // 确保有足够的调整空间
                indices.add(adjustabilityInfos.get(i).index);
            }
        }

        return indices;
    }

    /**
     * 可调整性信息类
     */
    private static class AdjustabilityInfo {
        int index;
        double value;
        double adjustability;

        AdjustabilityInfo(int index, double value, double adjustability) {
            this.index = index;
            this.value = value;
            this.adjustability = adjustability;
        }
    }

    /**
     * 全局均值微调 - 确保所有均值同时达标
     */
    private List<List<Double>> performGlobalMeanFineTuning(List<List<Double>> data,
                                                          List<List<Integer>> dimensions,
                                                          List<List<Double>> targetItemMeans,
                                                          List<Integer> allColumns) {
        log.info("[全局均值微调] 开始全局均值微调，确保所有均值同时达标");

        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        // 迭代调整，直到所有均值都达标
        int maxGlobalIterations = 10;
        for (int globalIter = 0; globalIter < maxGlobalIterations; globalIter++) {
            boolean allMeansAchieved = true;
            double maxDeviation = 0.0;

            // 检查所有均值的偏差
            for (int dimIdx = 0; dimIdx < dimensions.size(); dimIdx++) {
                List<Integer> dimension = dimensions.get(dimIdx);
                List<Double> dimTargetMeans = targetItemMeans.get(dimIdx);

                for (int itemIdx = 0; itemIdx < dimension.size(); itemIdx++) {
                    Integer col = dimension.get(itemIdx);
                    Double targetMean = dimTargetMeans.get(itemIdx);

                    if (targetMean == null) {
                        continue;
                    }

                    int dataIdx = getDataIndex(col, allColumns);
                    double actualMean = result.get(dataIdx).stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
                    double deviation = Math.abs(actualMean - targetMean);

                    maxDeviation = Math.max(maxDeviation, deviation);

                    if (deviation > 0.002) { // 更严格的标准
                        allMeansAchieved = false;

                        // 执行超微调
                        List<Double> ultraFineTuned = performUltraFineTuning(result.get(dataIdx), targetMean, currentScaleLevel);
                        result.set(dataIdx, ultraFineTuned);

                        log.debug("[全局均值微调] 题目{}微调，偏差: {:.4f} -> {:.4f}",
                                 col, deviation, Math.abs(ultraFineTuned.stream().mapToDouble(Double::doubleValue).average().orElse(0.0) - targetMean));
                    }
                }
            }

            log.info("[全局均值微调] 第{}轮全局微调完成，最大偏差: {:.4f}", globalIter + 1, maxDeviation);

            if (allMeansAchieved) {
                log.info("[全局均值微调] 所有均值已达标，全局微调完成");
                break;
            }
        }

        return result;
    }

    /**
     * 执行超微调 - 最后的精度保证
     */
    private List<Double> performUltraFineTuning(List<Double> data, Double targetMean, Integer scaleLevel) {
        List<Double> result = new ArrayList<>(data);
        double currentMean = result.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
        double gap = targetMean - currentMean;

        if (Math.abs(gap) < 0.0005) {
            return result; // 已经达到超高精度
        }

        // 使用最精确的调整策略：二分法调整
        for (int iteration = 0; iteration < 10; iteration++) {
            currentMean = result.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
            gap = targetMean - currentMean;

            if (Math.abs(gap) < 0.0005) {
                break;
            }

            // 找到一个最适合的调整点
            int bestIndex = findBestAdjustmentIndex(result, gap > 0, scaleLevel);
            if (bestIndex >= 0) {
                double currentValue = result.get(bestIndex);
                double adjustment = gap; // 直接使用gap作为调整量
                double newValue = currentValue + adjustment;
                newValue = Math.max(1.0, Math.min((double)scaleLevel, newValue));
                result.set(bestIndex, newValue);
            } else {
                break; // 无法找到合适的调整点
            }
        }

        return result;
    }

    /**
     * 找到最佳的单个调整点
     */
    private int findBestAdjustmentIndex(List<Double> data, boolean increaseDirection, Integer scaleLevel) {
        int bestIndex = -1;
        double bestAdjustability = 0.0;

        for (int i = 0; i < data.size(); i++) {
            double value = data.get(i);
            double adjustability;

            if (increaseDirection) {
                adjustability = scaleLevel - value; // 距离上边界的距离
            } else {
                adjustability = value - 1.0; // 距离下边界的距离
            }

            if (adjustability > bestAdjustability && adjustability > 0.1) {
                bestAdjustability = adjustability;
                bestIndex = i;
            }
        }

        return bestIndex;
    }


   
   
    /**
     * 根据强度调整单个题目均值
     */
    private List<Double> adjustSingleItemMeanWithIntensity(List<Double> data, Double targetMean, double intensity) {
        if (targetMean == null || intensity <= 0) {
            return new ArrayList<>(data);
        }

        List<Double> result = new ArrayList<>(data);
        double currentMean = result.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
        double gap = targetMean - currentMean;

        if (Math.abs(gap) < 0.01) {
            return result;
        }

        // 根据强度调整gap
        double adjustedGap = gap * intensity;

        // 选择调整的数据点数量（基于强度）
        int adjustCount = Math.max(1, (int) (result.size() * intensity * 0.3));

        // 随机选择数据点进行调整
        Random random = new Random(42);
        List<Integer> indices = new ArrayList<>();
        for (int i = 0; i < result.size(); i++) {
            indices.add(i);
        }
        java.util.Collections.shuffle(indices, random);

        for (int i = 0; i < Math.min(adjustCount, indices.size()); i++) {
            int idx = indices.get(i);
            double currentValue = result.get(idx);
            double adjustment = adjustedGap / adjustCount;
            double newValue = currentValue + adjustment;

            // 确保在量表范围内
            newValue = Math.max(1.0, Math.min((double)currentScaleLevel, newValue));
            result.set(idx, newValue);
        }

        return result;
    }

    /**
     * 超精确调整各维度信度 - 全新算法
     */
    private List<List<Double>> adjustDimensionAlphasIntelligently(List<List<Double>> data,
                                                                List<List<Integer>> dimensions,
                                                                List<Double> targetDimensionAlphas,
                                                                Double tolerance,
                                                                double adjustmentIntensity) {
        log.info("[超精确信度调整] 开始超精确调整各维度信度，强度: {:.3f}", adjustmentIntensity);

        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        List<Integer> allColumns = getAllColumns(dimensions);

        // 第一轮：独立调整每个维度到接近目标值
        for (int dimIdx = 0; dimIdx < dimensions.size(); dimIdx++) {
            List<Integer> dimension = dimensions.get(dimIdx);
            double targetAlpha = targetDimensionAlphas.get(dimIdx);

            List<List<Double>> dimensionData = new ArrayList<>();
            for (Integer col : dimension) {
                int dataIdx = getDataIndex(col, allColumns);
                dimensionData.add(result.get(dataIdx));
            }

            double currentAlpha = calculateCronbachAlpha(dimensionData);
            double alphaGap = Math.abs(currentAlpha - targetAlpha);

            if (alphaGap > tolerance) {
                log.info("[超精确信度调整] 维度{}需要调整，当前: {:.4f}, 目标: {:.4f}, 差距: {:.4f}",
                         dimIdx + 1, currentAlpha, targetAlpha, alphaGap);

                // 使用超精确策略调整维度信度
                List<List<Double>> adjustedDimensionData = adjustSingleDimensionUltraPrecisely(
                        dimensionData, targetAlpha, tolerance, adjustmentIntensity, "维度" + (dimIdx + 1));

                // 将调整后的数据放回原位置
                for (int i = 0; i < dimension.size(); i++) {
                    Integer col = dimension.get(i);
                    int dataIdx = getDataIndex(col, allColumns);
                    result.set(dataIdx, adjustedDimensionData.get(i));
                }

                // 验证调整结果
                double finalAlpha = calculateCronbachAlpha(adjustedDimensionData);
                log.info("[超精确信度调整] 维度{}第一轮调整完成，{:.4f} -> {:.4f}, 目标: {:.4f}",
                         dimIdx + 1, currentAlpha, finalAlpha, targetAlpha);
            }
        }

        // 第二轮：全局协调调整，确保所有维度同时达标
        result = performGlobalDimensionAlphaCoordination(result, dimensions, targetDimensionAlphas,
                                                        tolerance, adjustmentIntensity, allColumns);

        // 第三轮：精细微调，确保每个维度都精确达标
        result = performFinalDimensionAlphaFineTuning(result, dimensions, targetDimensionAlphas,
                                                     tolerance, allColumns);

        return result;
    }

    /**
     * 激进调整单个维度信度 - 强力保证达标
     */
    private List<List<Double>> adjustSingleDimensionUltraPrecisely(List<List<Double>> data,
                                                                  double targetAlpha,
                                                                  Double tolerance,
                                                                  double adjustmentIntensity,
                                                                  String dimensionName) {
        log.info("[激进信度调整] {}开始激进调整，目标信度: {:.4f}", dimensionName, targetAlpha);

        List<List<Double>> result = ensureDataVariabilityOptimized(data);
        double currentAlpha = calculateCronbachAlpha(result);
        double initialAlpha = currentAlpha;
        double alphaGap = targetAlpha - currentAlpha;

        if (Math.abs(alphaGap) <= tolerance) {
            log.info("[激进信度调整] {}信度已达标", dimensionName);
            return result;
        }

        log.info("[激进信度调整] {}当前信度: {:.4f}, 目标: {:.4f}, 差距: {:.4f} - 启动激进模式",
                dimensionName, currentAlpha, targetAlpha, Math.abs(alphaGap));

        // 根据差距大小选择激进程度
        if (Math.abs(alphaGap) > 0.05) {
            // 差距很大，使用超激进策略
            log.info("[激进信度调整] {}差距较大(>{:.3f})，使用超激进策略", dimensionName, 0.05);
            result = performUltraAggressiveAlphaAdjustment(result, targetAlpha, alphaGap, dimensionName);
        } else if (Math.abs(alphaGap) > 0.02) {
            // 差距中等，使用激进策略
            log.info("[激进信度调整] {}差距中等(>{:.3f})，使用激进策略", dimensionName, 0.02);
            result = performAggressiveAlphaAdjustment(result, targetAlpha, alphaGap, dimensionName);
        } else {
            // 差距较小，使用精确策略
            log.info("[激进信度调整] {}差距较小，使用精确策略", dimensionName);
            result = performPreciseAlphaAdjustment(result, targetAlpha, tolerance, dimensionName);
        }

        // 最终验证和微调
        result = performFinalAlphaVerification(result, targetAlpha, tolerance, dimensionName);

        double finalAlpha = calculateCronbachAlpha(result);
        double finalGap = Math.abs(finalAlpha - targetAlpha);
        String status = finalGap <= tolerance ? "✓成功达标" : "仍需调整";

        log.info("[激进信度调整] {}调整完成，{:.4f} -> {:.4f}, 目标: {:.4f}, 偏差: {:.4f} - {}",
                 dimensionName, initialAlpha, finalAlpha, targetAlpha, finalGap, status);

        return result;
    }

    /**
     * 超激进信度调整 - 针对差距很大的情况
     */
    private List<List<Double>> performUltraAggressiveAlphaAdjustment(List<List<Double>> data,
                                                                   double targetAlpha,
                                                                   double alphaGap,
                                                                   String dimensionName) {
        log.info("[超激进调整] {}开始超激进调整，差距: {:.4f}", dimensionName, Math.abs(alphaGap));

        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        if (alphaGap > 0) {
            // 需要大幅提高信度：强力增强相关性
            log.info("[超激进调整] {}需要大幅提高信度，强力增强相关性", dimensionName);

            // 多轮强化相关性
            for (int round = 0; round < 5; round++) {
                double enhancementStrength = 0.4 + round * 0.1; // 递增强度

                for (int i = 0; i < result.size() - 1; i++) {
                    for (int j = i + 1; j < result.size(); j++) {
                        enhanceCorrelationAggressively(result.get(i), result.get(j), enhancementStrength);
                    }
                }

                // 检查进度
                double currentAlpha = calculateCronbachAlpha(result);
                log.debug("[超激进调整] {}第{}轮强化后信度: {:.4f}", dimensionName, round + 1, currentAlpha);

                if (Math.abs(currentAlpha - targetAlpha) < 0.02) {
                    break; // 已经接近目标
                }
            }

            // 额外的方差标准化
            result = standardizeVariancesForHigherAlpha(result);

        } else {
            // 需要大幅降低信度：强力增加差异性
            log.info("[超激进调整] {}需要大幅降低信度，强力增加差异性", dimensionName);

            double noiseStrength = Math.min(0.5, Math.abs(alphaGap) * 3.0);
            result = addStrongDiversityNoise(result, noiseStrength);
        }

        double finalAlpha = calculateCronbachAlpha(result);
        log.info("[超激进调整] {}超激进调整完成，信度: {:.4f}", dimensionName, finalAlpha);

        return result;
    }

    /**
     * 激进信度调整 - 针对差距中等的情况
     */
    private List<List<Double>> performAggressiveAlphaAdjustment(List<List<Double>> data,
                                                              double targetAlpha,
                                                              double alphaGap,
                                                              String dimensionName) {
        log.info("[激进调整] {}开始激进调整，差距: {:.4f}", dimensionName, Math.abs(alphaGap));

        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        if (alphaGap > 0) {
            // 需要提高信度：激进增强相关性
            double enhancementStrength = Math.min(0.3, Math.abs(alphaGap) * 2.0);

            // 多轮增强
            for (int round = 0; round < 3; round++) {
                for (int i = 0; i < result.size() - 1; i++) {
                    for (int j = i + 1; j < result.size(); j++) {
                        enhanceCorrelationAggressively(result.get(i), result.get(j), enhancementStrength);
                    }
                }

                double currentAlpha = calculateCronbachAlpha(result);
                if (Math.abs(currentAlpha - targetAlpha) < 0.01) {
                    break;
                }
            }

        } else {
            // 需要降低信度：激进增加差异性
            double noiseStrength = Math.min(0.3, Math.abs(alphaGap) * 2.0);
            result = addModerateDiversityNoise(result, noiseStrength);
        }

        double finalAlpha = calculateCronbachAlpha(result);
        log.info("[激进调整] {}激进调整完成，信度: {:.4f}", dimensionName, finalAlpha);

        return result;
    }

    /**
     * 最终验证和微调
     */
    private List<List<Double>> performFinalAlphaVerification(List<List<Double>> data,
                                                           double targetAlpha,
                                                           Double tolerance,
                                                           String dimensionName) {
        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        double currentAlpha = calculateCronbachAlpha(result);
        double alphaGap = targetAlpha - currentAlpha;

        if (Math.abs(alphaGap) <= tolerance) {
            log.debug("[最终验证] {}已达标，无需微调", dimensionName);
            return result;
        }

        log.info("[最终验证] {}仍需微调，差距: {:.4f}", dimensionName, Math.abs(alphaGap));

        // 最后的精细微调
        for (int iteration = 0; iteration < 10; iteration++) {
            currentAlpha = calculateCronbachAlpha(result);
            alphaGap = targetAlpha - currentAlpha;

            if (Math.abs(alphaGap) <= tolerance) {
                break;
            }

            // 使用最小的调整量
            if (alphaGap > 0) {
                // 微量增强相关性
                for (int i = 0; i < result.size() - 1; i++) {
                    for (int j = i + 1; j < result.size(); j++) {
                        enhanceCorrelationAggressively(result.get(i), result.get(j), 0.05);
                    }
                }
            } else {
                // 微量增加差异
                result = addMicroDiversityNoise(result, 0.02);
            }
        }

        double finalAlpha = calculateCronbachAlpha(result);
        log.info("[最终验证] {}微调完成，最终信度: {:.4f}", dimensionName, finalAlpha);

        return result;
    }

   
    /**
     * 精确调整信度 - 数学优化方法
     */
    private List<List<Double>> performPreciseAlphaAdjustment(List<List<Double>> data,
                                                           double targetAlpha,
                                                           Double tolerance,
                                                           String dimensionName) {
        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        int maxIterations = 30;
        for (int iteration = 0; iteration < maxIterations; iteration++) {
            double currentAlpha = calculateCronbachAlpha(result);
            double alphaGap = targetAlpha - currentAlpha;

            if (Math.abs(alphaGap) <= tolerance) {
                log.debug("[精确调整] {}第{}次迭代达到目标", dimensionName, iteration);
                break;
            }

            // 使用梯度下降思想进行精确调整
            result = performGradientBasedAlphaAdjustment(result, targetAlpha, iteration);

            // 确保数据变异性
            result = ensureDataVariabilityOptimized(result);
        }

        return result;
    }

   
    /**
     * 激进增强两个题目间的相关性
     */
    private void enhanceCorrelationAggressively(List<Double> item1, List<Double> item2, double strength) {
        if (item1.size() != item2.size()) return;

        // 计算当前相关性
        double currentCorr = calculateCorrelation(item1, item2);

        log.debug("[激进相关性增强] 当前相关性: {:.3f}, 增强强度: {:.3f}", currentCorr, strength);

        // 使用更激进的方法：直接向目标相关性调整
        for (int i = 0; i < item1.size(); i++) {
            double val1 = item1.get(i);
            double val2 = item2.get(i);

            // 计算两个值的加权平均，权重由强度决定
            double weight = strength;
            double avg = (val1 + val2) / 2.0;

            double newVal1 = val1 * (1 - weight) + avg * weight;
            double newVal2 = val2 * (1 - weight) + avg * weight;

            // 确保在量表范围内
            newVal1 = Math.max(1.0, Math.min((double)currentScaleLevel, newVal1));
            newVal2 = Math.max(1.0, Math.min((double)currentScaleLevel, newVal2));

            item1.set(i, newVal1);
            item2.set(i, newVal2);
        }
    }

    /**
     * 方差标准化以提高信度
     */
    private List<List<Double>> standardizeVariancesForHigherAlpha(List<List<Double>> data) {
        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        // 计算目标方差（所有题目方差的平均值）
        double totalVariance = 0.0;
        int validCount = 0;

        for (List<Double> col : result) {
            DescriptiveStatistics stats = new DescriptiveStatistics();
            col.forEach(stats::addValue);
            double variance = stats.getVariance();
            if (!Double.isNaN(variance) && variance > 0) {
                totalVariance += variance;
                validCount++;
            }
        }

        if (validCount == 0) return result;

        double targetVariance = totalVariance / validCount;
        log.debug("[方差标准化] 目标方差: {:.4f}", targetVariance);

        // 调整每个题目的方差接近目标方差
        for (List<Double> col : result) {
            adjustVarianceToTargetAggressively(col, targetVariance);
        }

        return result;
    }

    /**
     * 激进调整方差到目标值
     */
    private void adjustVarianceToTargetAggressively(List<Double> data, double targetVariance) {
        DescriptiveStatistics stats = new DescriptiveStatistics();
        data.forEach(stats::addValue);

        double currentVariance = stats.getVariance();
        double mean = stats.getMean();

        if (Double.isNaN(currentVariance) || currentVariance <= 0.001) {
            return; // 方差太小，无法调整
        }

        // 计算激进的调整因子
        double adjustmentFactor = Math.sqrt(targetVariance / currentVariance);

        // 限制调整因子在合理范围内，但允许较大变化
        adjustmentFactor = Math.max(0.3, Math.min(3.0, adjustmentFactor));

        log.debug("[激进方差调整] 当前方差: {:.4f}, 目标方差: {:.4f}, 调整因子: {:.3f}",
                 currentVariance, targetVariance, adjustmentFactor);

        // 调整每个数据点
        for (int i = 0; i < data.size(); i++) {
            double value = data.get(i);
            double deviation = value - mean;
            double newValue = mean + deviation * adjustmentFactor;
            newValue = Math.max(1.0, Math.min((double)currentScaleLevel, newValue));
            data.set(i, newValue);
        }
    }

    /**
     * 添加强差异性噪声
     */
    private List<List<Double>> addStrongDiversityNoise(List<List<Double>> data, double noiseStrength) {
        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        Random random = new Random(42);

        // 为每个题目添加不同模式的强噪声
        for (int colIdx = 0; colIdx < result.size(); colIdx++) {
            List<Double> col = result.get(colIdx);

            // 每个题目使用不同的噪声模式
            for (int i = 0; i < col.size(); i++) {
                // 使用多种噪声源
                double gaussianNoise = random.nextGaussian() * noiseStrength;
                double sinusoidalNoise = Math.sin(i * 0.1 + colIdx * Math.PI / result.size()) * noiseStrength * 0.5;
                double combinedNoise = gaussianNoise + sinusoidalNoise;

                double newValue = col.get(i) + combinedNoise;
                newValue = Math.max(1.0, Math.min((double)currentScaleLevel, newValue));
                col.set(i, newValue);
            }
        }

        log.debug("[强差异性噪声] 添加强度: {:.3f}", noiseStrength);
        return result;
    }

    /**
     * 添加中等差异性噪声
     */
    private List<List<Double>> addModerateDiversityNoise(List<List<Double>> data, double noiseStrength) {
        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        Random random = new Random(42);

        for (int colIdx = 0; colIdx < result.size(); colIdx++) {
            List<Double> col = result.get(colIdx);

            for (int i = 0; i < col.size(); i++) {
                double noise = random.nextGaussian() * noiseStrength;
                double newValue = col.get(i) + noise;
                newValue = Math.max(1.0, Math.min((double)currentScaleLevel, newValue));
                col.set(i, newValue);
            }
        }

        return result;
    }

    /**
     * 添加微量差异性噪声
     */
    private List<List<Double>> addMicroDiversityNoise(List<List<Double>> data, double noiseStrength) {
        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        Random random = new Random(42);

        // 只调整少数数据点
        for (List<Double> col : result) {
            int adjustCount = Math.max(1, col.size() / 20); // 只调整5%的数据点

            for (int i = 0; i < adjustCount; i++) {
                int idx = random.nextInt(col.size());
                double noise = random.nextGaussian() * noiseStrength;
                double newValue = col.get(idx) + noise;
                newValue = Math.max(1.0, Math.min((double)currentScaleLevel, newValue));
                col.set(idx, newValue);
            }
        }

        return result;
    }

   

    /**
     * 精确增强两个题目间的相关性
     */
    private void enhanceCorrelationBetweenItemsPrecisely(List<Double> item1, List<Double> item2, double strength) {
        if (item1.size() != item2.size()) return;

        // 计算当前相关性
        double currentCorr = calculateCorrelation(item1, item2);

        // 如果相关性已经很高，减少调整强度
        if (currentCorr > 0.8) {
            strength *= 0.3;
        }

        // 使用加权平均方法增强相关性
        for (int i = 0; i < item1.size(); i++) {
            double val1 = item1.get(i);
            double val2 = item2.get(i);

            // 计算加权平均，使两个值更接近
            double avg = (val1 + val2) / 2.0;
            double newVal1 = val1 + (avg - val1) * strength;
            double newVal2 = val2 + (avg - val2) * strength;

            // 确保在量表范围内
            newVal1 = Math.max(1.0, Math.min((double)currentScaleLevel, newVal1));
            newVal2 = Math.max(1.0, Math.min((double)currentScaleLevel, newVal2));

            item1.set(i, newVal1);
            item2.set(i, newVal2);
        }
    }

   
    /**
     * 基于梯度的信度调整
     */
    private List<List<Double>> performGradientBasedAlphaAdjustment(List<List<Double>> data,
                                                                  double targetAlpha,
                                                                  int iteration) {
        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        double currentAlpha = calculateCronbachAlpha(result);
        double alphaGap = targetAlpha - currentAlpha;

        // 动态学习率，随迭代次数递减
        double learningRate = 0.1 * Math.exp(-iteration * 0.1);

        if (alphaGap > 0) {
            // 需要提高信度：通过调整方差和协方差
            result = adjustVariancesAndCovariancesForHigherAlpha(result, alphaGap, learningRate);
        } else {
            // 需要降低信度：增加题目间的差异性
            result = increaseDiversityForLowerAlpha(result, Math.abs(alphaGap), learningRate);
        }

        return result;
    }

    /**
     * 调整方差和协方差以提高信度
     */
    private List<List<Double>> adjustVariancesAndCovariancesForHigherAlpha(List<List<Double>> data,
                                                                          double alphaGap,
                                                                          double learningRate) {
        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        // 计算目标方差（使各题目方差更相似）
        double totalVariance = 0.0;
        for (List<Double> col : result) {
            DescriptiveStatistics stats = new DescriptiveStatistics();
            col.forEach(stats::addValue);
            totalVariance += stats.getVariance();
        }
        double targetVariance = totalVariance / result.size();

        // 调整每个题目的方差接近目标方差
        for (List<Double> col : result) {
            adjustVarianceToTarget(col, targetVariance, learningRate * alphaGap);
        }

        return result;
    }

    /**
     * 调整单个题目的方差到目标值
     */
    private void adjustVarianceToTarget(List<Double> data, double targetVariance, double strength) {
        DescriptiveStatistics stats = new DescriptiveStatistics();
        data.forEach(stats::addValue);

        double currentVariance = stats.getVariance();
        double mean = stats.getMean();

        if (Math.abs(currentVariance - targetVariance) < 0.01) {
            return; // 已经很接近了
        }

        // 计算调整因子
        double adjustmentFactor = Math.sqrt(targetVariance / Math.max(currentVariance, 0.01));
        adjustmentFactor = 1.0 + (adjustmentFactor - 1.0) * strength;

        // 调整每个数据点
        for (int i = 0; i < data.size(); i++) {
            double value = data.get(i);
            double deviation = value - mean;
            double newValue = mean + deviation * adjustmentFactor;
            newValue = Math.max(1.0, Math.min((double)currentScaleLevel, newValue));
            data.set(i, newValue);
        }
    }

    /**
     * 增加差异性以降低信度
     */
    private List<List<Double>> increaseDiversityForLowerAlpha(List<List<Double>> data,
                                                            double alphaGap,
                                                            double learningRate) {
        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        for (int colIdx = 0; colIdx < result.size(); colIdx++) {
            List<Double> col = result.get(colIdx);

            // 每个题目使用不同的变异模式
            double phaseShift = colIdx * Math.PI / result.size();

            for (int i = 0; i < col.size(); i++) {
                // 使用正弦函数创建有规律的差异
                double variation = Math.sin(i * 0.1 + phaseShift) * alphaGap * learningRate * 0.5;
                double newValue = col.get(i) + variation;
                newValue = Math.max(1.0, Math.min((double)currentScaleLevel, newValue));
                col.set(i, newValue);
            }
        }

        return result;
    }

    

    /**
     * 应用微量差异化
     */
    private List<List<Double>> applyMicroDiversification(List<List<Double>> data, double strength) {
        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        Random random = new Random(42);

        // 为每个题目添加微量的独特变异
        for (int colIdx = 0; colIdx < result.size(); colIdx++) {
            List<Double> col = result.get(colIdx);

            // 只调整少数数据点
            int adjustCount = Math.max(1, col.size() / 20); // 调整5%的数据点

            for (int i = 0; i < adjustCount; i++) {
                int idx = random.nextInt(col.size());
                double uniqueVariation = (colIdx + 1) * strength * (random.nextGaussian() * 0.5);
                double newValue = col.get(idx) + uniqueVariation;
                newValue = Math.max(1.0, Math.min((double)currentScaleLevel, newValue));
                col.set(idx, newValue);
            }
        }

        return result;
    }

    /**
     * 全局维度信度协调
     */
    private List<List<Double>> performGlobalDimensionAlphaCoordination(List<List<Double>> data,
                                                                     List<List<Integer>> dimensions,
                                                                     List<Double> targetDimensionAlphas,
                                                                     Double tolerance,
                                                                     double adjustmentIntensity,
                                                                     List<Integer> allColumns) {
        log.info("[全局协调] 开始全局维度信度协调");

        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        // 迭代协调，确保所有维度同时达标
        int maxCoordinationIterations = 8;
        for (int coordIter = 0; coordIter < maxCoordinationIterations; coordIter++) {
            boolean allDimensionsAchieved = true;
            double maxAlphaDeviation = 0.0;

            // 检查所有维度的信度偏差
            for (int dimIdx = 0; dimIdx < dimensions.size(); dimIdx++) {
                List<Integer> dimension = dimensions.get(dimIdx);
                double targetAlpha = targetDimensionAlphas.get(dimIdx);

                List<List<Double>> dimensionData = new ArrayList<>();
                for (Integer col : dimension) {
                    int dataIdx = getDataIndex(col, allColumns);
                    dimensionData.add(result.get(dataIdx));
                }

                double currentAlpha = calculateCronbachAlpha(dimensionData);
                double deviation = Math.abs(currentAlpha - targetAlpha);
                maxAlphaDeviation = Math.max(maxAlphaDeviation, deviation);

                if (deviation > tolerance) {
                    allDimensionsAchieved = false;

                    // 执行协调性调整（强度递减）
                    double coordAdjustmentIntensity = adjustmentIntensity * Math.exp(-coordIter * 0.2);
                    List<List<Double>> coordAdjustedData = performCoordinatedAlphaAdjustment(
                            dimensionData, targetAlpha, coordAdjustmentIntensity, "维度" + (dimIdx + 1));

                    // 将调整后的数据放回原位置
                    for (int i = 0; i < dimension.size(); i++) {
                        Integer col = dimension.get(i);
                        int dataIdx = getDataIndex(col, allColumns);
                        result.set(dataIdx, coordAdjustedData.get(i));
                    }

                    log.debug("[全局协调] 维度{}协调调整，偏差: {:.4f}", dimIdx + 1, deviation);
                }
            }

            log.info("[全局协调] 第{}轮协调完成，最大偏差: {:.4f}", coordIter + 1, maxAlphaDeviation);

            if (allDimensionsAchieved) {
                log.info("[全局协调] 所有维度信度已协调达标");
                break;
            }
        }

        return result;
    }

    /**
     * 执行协调性信度调整
     */
    private List<List<Double>> performCoordinatedAlphaAdjustment(List<List<Double>> data,
                                                               double targetAlpha,
                                                               double adjustmentIntensity,
                                                               String dimensionName) {
        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        double currentAlpha = calculateCronbachAlpha(result);
        double alphaGap = targetAlpha - currentAlpha;

        if (Math.abs(alphaGap) < 0.005) {
            return result; // 已经足够精确
        }

        // 使用温和的协调性调整
        if (alphaGap > 0) {
            // 轻微增强相关性
            for (int i = 0; i < result.size() - 1; i++) {
                for (int j = i + 1; j < result.size(); j++) {
                    enhanceCorrelationBetweenItemsPrecisely(result.get(i), result.get(j),
                                                          Math.abs(alphaGap) * adjustmentIntensity * 0.3);
                }
            }
        } else {
            // 轻微增加差异性
            result = applyMicroDiversification(result, Math.abs(alphaGap) * adjustmentIntensity * 0.3);
        }

        return result;
    }

    /**
     * 最终维度信度精细调整
     */
    private List<List<Double>> performFinalDimensionAlphaFineTuning(List<List<Double>> data,
                                                                  List<List<Integer>> dimensions,
                                                                  List<Double> targetDimensionAlphas,
                                                                  Double tolerance,
                                                                  List<Integer> allColumns) {
        log.info("[最终精调] 开始最终维度信度精细调整");

        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        // 对每个维度进行最终精调
        for (int dimIdx = 0; dimIdx < dimensions.size(); dimIdx++) {
            List<Integer> dimension = dimensions.get(dimIdx);
            double targetAlpha = targetDimensionAlphas.get(dimIdx);

            List<List<Double>> dimensionData = new ArrayList<>();
            for (Integer col : dimension) {
                int dataIdx = getDataIndex(col, allColumns);
                dimensionData.add(result.get(dataIdx));
            }

            double currentAlpha = calculateCronbachAlpha(dimensionData);
            double deviation = Math.abs(currentAlpha - targetAlpha);

            if (deviation > tolerance * 0.5) { // 更严格的标准
                log.debug("[最终精调] 维度{}需要最终精调，偏差: {:.4f}", dimIdx + 1, deviation);

                // 执行最精细的调整
                List<List<Double>> fineTunedData = performUltimateAlphaFineTuning(
                        dimensionData, targetAlpha, tolerance, "维度" + (dimIdx + 1));

                // 将调整后的数据放回原位置
                for (int i = 0; i < dimension.size(); i++) {
                    Integer col = dimension.get(i);
                    int dataIdx = getDataIndex(col, allColumns);
                    result.set(dataIdx, fineTunedData.get(i));
                }

                double finalAlpha = calculateCronbachAlpha(fineTunedData);
                log.info("[最终精调] 维度{}最终精调完成，{:.4f} -> {:.4f}, 目标: {:.4f}",
                         dimIdx + 1, currentAlpha, finalAlpha, targetAlpha);
            }
        }

        log.info("[最终精调] 最终维度信度精细调整完成");
        return result;
    }

    /**
     * 终极信度精调
     */
    private List<List<Double>> performUltimateAlphaFineTuning(List<List<Double>> data,
                                                            double targetAlpha,
                                                            Double tolerance,
                                                            String dimensionName) {
        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        // 使用二分法思想进行终极精调
        int maxUltimateIterations = 10;
        for (int iteration = 0; iteration < maxUltimateIterations; iteration++) {
            double currentAlpha = calculateCronbachAlpha(result);
            double alphaGap = targetAlpha - currentAlpha;

            if (Math.abs(alphaGap) <= tolerance * 0.3) { // 超高精度标准
                log.debug("[终极精调] {}第{}次迭代达到超高精度", dimensionName, iteration);
                break;
            }

            // 使用最小的调整量
            double ultimateAdjustment = Math.min(0.005, Math.abs(alphaGap) * 0.5);

            if (alphaGap > 0) {
                // 极微量增强相关性
                result = applyUltimateCorrelationEnhancement(result, ultimateAdjustment);
            } else {
                // 极微量增加差异
                result = applyUltimateDiversification(result, ultimateAdjustment);
            }

            // 确保数据变异性
            result = ensureDataVariabilityOptimized(result);
        }

        return result;
    }

    /**
     * 应用终极相关性增强
     */
    private List<List<Double>> applyUltimateCorrelationEnhancement(List<List<Double>> data, double strength) {
        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        // 只调整最关键的几个数据点
        Random random = new Random(42);
        int totalAdjustments = Math.max(1, data.get(0).size() / 50); // 只调整2%的数据点

        for (int adj = 0; adj < totalAdjustments; adj++) {
            int idx = random.nextInt(data.get(0).size());

            // 计算所有题目在该位置的平均值
            double avg = 0.0;
            for (List<Double> col : result) {
                avg += col.get(idx);
            }
            avg /= result.size();

            // 让所有题目的值都向平均值靠拢一点点
            for (List<Double> col : result) {
                double currentValue = col.get(idx);
                double newValue = currentValue + (avg - currentValue) * strength;
                newValue = Math.max(1.0, Math.min((double)currentScaleLevel, newValue));
                col.set(idx, newValue);
            }
        }

        return result;
    }

    /**
     * 应用终极差异化
     */
    private List<List<Double>> applyUltimateDiversification(List<List<Double>> data, double strength) {
        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        Random random = new Random(42);

        // 为每个题目添加极微量的独特变异
        for (int colIdx = 0; colIdx < result.size(); colIdx++) {
            List<Double> col = result.get(colIdx);

            // 只调整极少数数据点
            int adjustCount = Math.max(1, col.size() / 100); // 只调整1%的数据点

            for (int i = 0; i < adjustCount; i++) {
                int idx = random.nextInt(col.size());
                double uniqueVariation = (colIdx + 1) * strength * random.nextGaussian() * 0.1;
                double newValue = col.get(idx) + uniqueVariation;
                newValue = Math.max(1.0, Math.min((double)currentScaleLevel, newValue));
                col.set(idx, newValue);
            }
        }

        return result;
    }

    /**
     * 使用智能策略调整单个维度信度
     */
    private List<List<Double>> adjustSingleDimensionWithIntelligentStrategy(List<List<Double>> data,
                                                                           double targetAlpha,
                                                                           Double tolerance,
                                                                           double adjustmentIntensity,
                                                                           String dimensionName) {
        List<List<Double>> result = ensureDataVariabilityOptimized(data);

        double currentAlpha = calculateCronbachAlpha(result);
        double alphaGap = targetAlpha - currentAlpha;

        if (Math.abs(alphaGap) <= tolerance) {
            return result;
        }

        log.debug("[智能策略] {}开始智能调整，当前信度: {:.3f}, 目标: {:.3f}, 强度: {:.3f}",
                 dimensionName, currentAlpha, targetAlpha, adjustmentIntensity);

        // 根据调整强度选择策略
        if (adjustmentIntensity > 0.7) {
            // 高强度：使用激进策略
            result = adjustWithAggressiveStrategy(result, targetAlpha, alphaGap);
        } else if (adjustmentIntensity > 0.4) {
            // 中等强度：使用平衡策略
            result = adjustWithBalancedStrategy(result, targetAlpha, alphaGap);
        } else {
            // 低强度：使用保守策略
            result = adjustWithConservativeStrategy(result, targetAlpha, alphaGap);
        }

        // 确保数据变异性
        result = ensureDataVariabilityOptimized(result);

        double finalAlpha = calculateCronbachAlpha(result);
        log.debug("[智能策略] {}调整完成，{:.3f} -> {:.3f}", dimensionName, currentAlpha, finalAlpha);

        return result;
    }

    /**
     * 激进调整策略
     */
    private List<List<Double>> adjustWithAggressiveStrategy(List<List<Double>> data, double targetAlpha, double alphaGap) {
        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        if (alphaGap > 0) {
            // 需要提高信度：强化相关性
            for (int i = 0; i < result.size() - 1; i++) {
                for (int j = i + 1; j < result.size(); j++) {
                    enhanceCorrelationBetweenItems(result.get(i), result.get(j), 0.2);
                }
            }
        } else {
            // 需要降低信度：增加噪声
            for (List<Double> col : result) {
                addControlledNoise(col, 0.15);
            }
        }

        return result;
    }

    /**
     * 平衡调整策略
     */
    private List<List<Double>> adjustWithBalancedStrategy(List<List<Double>> data, double targetAlpha, double alphaGap) {
        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        if (alphaGap > 0) {
            // 适度提高信度
            for (int i = 0; i < result.size() - 1; i++) {
                for (int j = i + 1; j < result.size(); j++) {
                    enhanceCorrelationBetweenItems(result.get(i), result.get(j), 0.1);
                }
            }
        } else {
            // 适度降低信度
            for (List<Double> col : result) {
                addControlledNoise(col, 0.08);
            }
        }

        return result;
    }

    /**
     * 保守调整策略
     */
    private List<List<Double>> adjustWithConservativeStrategy(List<List<Double>> data, double targetAlpha, double alphaGap) {
        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        if (alphaGap > 0) {
            // 轻微提高信度
            for (int i = 0; i < result.size() - 1; i++) {
                for (int j = i + 1; j < result.size(); j++) {
                    enhanceCorrelationBetweenItems(result.get(i), result.get(j), 0.05);
                }
            }
        } else {
            // 轻微降低信度
            for (List<Double> col : result) {
                addControlledNoise(col, 0.03);
            }
        }

        return result;
    }

    /**
     * 添加受控噪声
     */
    private void addControlledNoise(List<Double> col, double noiseLevel) {
        Random random = new Random(42);
        for (int i = 0; i < col.size(); i++) {
            double noise = random.nextGaussian() * noiseLevel;
            double newValue = col.get(i) + noise;
            newValue = Math.max(1.0, Math.min((double)currentScaleLevel, newValue));
            col.set(i, newValue);
        }
    }


    /**
     * 增强两个项目间的相关性
     */
    private void enhanceCorrelationBetweenItems(List<Double> item1, List<Double> item2, double targetIncrease) {
        if (item1.size() != item2.size()) return;

        // 通过调整数据点来增强相关性
        for (int i = 0; i < item1.size(); i++) {
            if (Math.random() < 0.3) { // 30%的概率调整
                double val1 = item1.get(i);
                double val2 = item2.get(i);

                // 让两个值更接近
                double avg = (val1 + val2) / 2.0;
                double adjustment = targetIncrease * 0.5;

                double newVal1 = val1 + (avg - val1) * adjustment;
                double newVal2 = val2 + (avg - val2) * adjustment;

                // 使用当前设置的量表级数并限制范围
                newVal1 = Math.max(1.0, Math.min((double)currentScaleLevel, newVal1));
                newVal2 = Math.max(1.0, Math.min((double)currentScaleLevel, newVal2));

                item1.set(i, newVal1);
                item2.set(i, newVal2);
            }
        }
    }


    /**
     * 智能调整总量表信度 - 优化版本
     */
    private List<List<Double>> adjustTotalAlphaIntelligentlyOptimized(List<List<Double>> data,
                                                                    Double targetTotalAlpha,
                                                                    Double tolerance,
                                                                    double adjustmentIntensity) {
        log.debug("[智能总信度调整] 开始优化调整总量表信度，目标: {}, 强度: {:.3f}", targetTotalAlpha, adjustmentIntensity);

        double currentAlpha = calculateCronbachAlpha(data);

        if (Math.abs(currentAlpha - targetTotalAlpha) <= tolerance) {
            log.debug("[智能总信度调整] 总信度已达标");
            return data;
        }

        return adjustSingleDimensionWithIntelligentStrategy(data, targetTotalAlpha, tolerance, adjustmentIntensity, "总量表");
    }

    /**
     * 激进调整KMO值 - 强力保证达标
     */
    private List<List<Double>> adjustKMOIntelligentlyOptimized(List<List<Double>> data,
                                                             Double targetKMO,
                                                             Double tolerance,
                                                             double adjustmentIntensity) {
        log.info("[激进KMO调整] 开始激进调整KMO值，目标: {:.3f}, 强度: {:.3f}", targetKMO, adjustmentIntensity);

        List<List<Double>> result = ensureDataVariabilityOptimized(data);

        double currentKMO = calculateKMOFromAdjustedData(result);
        double initialKMO = currentKMO;

        if (Double.isNaN(currentKMO) || currentKMO == 0.0) {
            log.warn("[激进KMO调整] 当前KMO无效，进行强力数据修复");
            result = repairDataForKMOAggressively(result);
            currentKMO = calculateKMOFromAdjustedData(result);
        }

        if (Math.abs(currentKMO - targetKMO) <= tolerance) {
            log.info("[激进KMO调整] KMO已达标");
            return result;
        }

        double kmoGap = targetKMO - currentKMO;
        log.info("[激进KMO调整] 当前KMO: {:.3f}, 目标: {:.3f}, 差距: {:.3f} - 启动激进模式",
                currentKMO, targetKMO, kmoGap);

        // 根据差距大小选择激进程度
        if (Math.abs(kmoGap) > 0.08) {
            // 差距很大，使用超激进策略
            log.info("[激进KMO调整] 差距很大(>{:.3f})，使用超激进KMO策略", 0.08);
            result = performUltraAggressiveKMOAdjustment(result, targetKMO, kmoGap);
        } else if (Math.abs(kmoGap) > 0.03) {
            // 差距中等，使用激进策略
            log.info("[激进KMO调整] 差距中等(>{:.3f})，使用激进KMO策略", 0.03);
            result = performAggressiveKMOAdjustment(result, targetKMO, kmoGap);
        } else {
            // 差距较小，使用精确策略
            log.info("[激进KMO调整] 差距较小，使用精确KMO策略");
            result = performPreciseKMOAdjustment(result, targetKMO, tolerance);
        }

        // 最终验证
        result = ensureDataVariabilityOptimized(result);
        double finalKMO = calculateKMOFromAdjustedData(result);
        double finalGap = Math.abs(finalKMO - targetKMO);
        String status = finalGap <= tolerance ? "✓成功达标" : "仍需调整";

        log.info("[激进KMO调整] KMO调整完成，{:.3f} -> {:.3f}, 目标: {:.3f}, 偏差: {:.3f} - {}",
                 initialKMO, finalKMO, targetKMO, finalGap, status);

        return result;
    }

    /**
     * 激进修复KMO数据
     */
    private List<List<Double>> repairDataForKMOAggressively(List<List<Double>> data) {
        log.info("[激进KMO修复] 开始激进修复KMO数据");

        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        // 强力确保数据变异性
        result = ensureDataVariabilityOptimized(result);

        // 多轮强化相关性
        for (int round = 0; round < 3; round++) {
            for (int i = 0; i < result.size() - 1; i++) {
                for (int j = i + 1; j < result.size(); j++) {
                    enhanceCorrelationAggressively(result.get(i), result.get(j), 0.3);
                }
            }

            // 检查KMO是否有效
            double kmo = calculateKMOFromAdjustedData(result);
            if (!Double.isNaN(kmo) && kmo > 0.5) {
                log.info("[激进KMO修复] 第{}轮修复后KMO: {:.3f}", round + 1, kmo);
                break;
            }
        }

        return result;
    }

    /**
     * 超激进KMO调整
     */
    private List<List<Double>> performUltraAggressiveKMOAdjustment(List<List<Double>> data,
                                                                 Double targetKMO,
                                                                 double kmoGap) {
        log.info("[超激进KMO] 开始超激进KMO调整，差距: {:.3f}", Math.abs(kmoGap));

        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        if (kmoGap > 0) {
            // 需要大幅提高KMO：强力增强所有变量间相关性
            log.info("[超激进KMO] 需要大幅提高KMO，强力增强相关性");

            // 多轮超强化
            for (int round = 0; round < 8; round++) {
                double enhancementStrength = 0.4 + round * 0.05; // 递增强度

                // 增强所有变量对之间的相关性
                for (int i = 0; i < result.size() - 1; i++) {
                    for (int j = i + 1; j < result.size(); j++) {
                        enhanceCorrelationAggressively(result.get(i), result.get(j), enhancementStrength);
                    }
                }

                // 检查进度
                double currentKMO = calculateKMOFromAdjustedData(result);
                log.debug("[超激进KMO] 第{}轮强化后KMO: {:.3f}", round + 1, currentKMO);

                if (!Double.isNaN(currentKMO) && Math.abs(currentKMO - targetKMO) < 0.03) {
                    break; // 已经接近目标
                }
            }

            // 额外的协方差矩阵优化
            result = optimizeCovarianceMatrixForKMO(result, targetKMO);

        } else {
            // 需要降低KMO：适度增加变量间差异
            log.info("[超激进KMO] 需要降低KMO，增加变量差异");
            result = addStrongDiversityNoise(result, Math.min(0.3, Math.abs(kmoGap) * 2.0));
        }

        double finalKMO = calculateKMOFromAdjustedData(result);
        log.info("[超激进KMO] 超激进KMO调整完成，KMO: {:.3f}", finalKMO);

        return result;
    }

    /**
     * 激进KMO调整
     */
    private List<List<Double>> performAggressiveKMOAdjustment(List<List<Double>> data,
                                                            Double targetKMO,
                                                            double kmoGap) {
        log.info("[激进KMO] 开始激进KMO调整，差距: {:.3f}", Math.abs(kmoGap));

        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        if (kmoGap > 0) {
            // 需要提高KMO：激进增强相关性
            double enhancementStrength = Math.min(0.4, Math.abs(kmoGap) * 3.0);

            // 多轮增强
            for (int round = 0; round < 5; round++) {
                for (int i = 0; i < result.size() - 1; i++) {
                    for (int j = i + 1; j < result.size(); j++) {
                        enhanceCorrelationAggressively(result.get(i), result.get(j), enhancementStrength);
                    }
                }

                double currentKMO = calculateKMOFromAdjustedData(result);
                if (!Double.isNaN(currentKMO) && Math.abs(currentKMO - targetKMO) < 0.02) {
                    break;
                }
            }

        } else {
            // 需要降低KMO：激进增加差异性
            double noiseStrength = Math.min(0.2, Math.abs(kmoGap) * 2.0);
            result = addModerateDiversityNoise(result, noiseStrength);
        }

        double finalKMO = calculateKMOFromAdjustedData(result);
        log.info("[激进KMO] 激进KMO调整完成，KMO: {:.3f}", finalKMO);

        return result;
    }

    /**
     * 精确KMO调整
     */
    private List<List<Double>> performPreciseKMOAdjustment(List<List<Double>> data,
                                                         Double targetKMO,
                                                         Double tolerance) {
        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        double currentKMO = calculateKMOFromAdjustedData(result);
        double kmoGap = targetKMO - currentKMO;

        if (Math.abs(kmoGap) <= tolerance) {
            return result;
        }

        // 精确微调
        for (int iteration = 0; iteration < 15; iteration++) {
            currentKMO = calculateKMOFromAdjustedData(result);
            kmoGap = targetKMO - currentKMO;

            if (Math.abs(kmoGap) <= tolerance) {
                break;
            }

            if (kmoGap > 0) {
                // 微量增强相关性
                for (int i = 0; i < result.size() - 1; i++) {
                    for (int j = i + 1; j < result.size(); j++) {
                        enhanceCorrelationAggressively(result.get(i), result.get(j), 0.05);
                    }
                }
            } else {
                // 微量增加差异
                result = addMicroDiversityNoise(result, 0.02);
            }
        }

        return result;
    }

    /**
     * 优化协方差矩阵以提高KMO
     */
    private List<List<Double>> optimizeCovarianceMatrixForKMO(List<List<Double>> data, Double targetKMO) {
        log.debug("[协方差优化] 开始优化协方差矩阵以提高KMO");

        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        // 计算当前相关性矩阵
        double[][] correlationMatrix = new double[result.size()][result.size()];
        for (int i = 0; i < result.size(); i++) {
            for (int j = 0; j < result.size(); j++) {
                if (i == j) {
                    correlationMatrix[i][j] = 1.0;
                } else {
                    correlationMatrix[i][j] = calculateCorrelation(result.get(i), result.get(j));
                }
            }
        }

        // 强化弱相关性
        for (int i = 0; i < result.size() - 1; i++) {
            for (int j = i + 1; j < result.size(); j++) {
                double currentCorr = correlationMatrix[i][j];
                if (Math.abs(currentCorr) < 0.3) { // 相关性太弱
                    enhanceCorrelationAggressively(result.get(i), result.get(j), 0.2);
                }
            }
        }

        return result;
    }


    /**
     * 智能调整维度间相关性 - 优化版本
     */
    private List<List<Double>> adjustInterDimensionCorrelationsOptimized(List<List<Double>> data,
                                                                       List<List<Integer>> dimensions,
                                                                       Double targetInterDimensionCorrelation,
                                                                       double adjustmentIntensity) {
        log.debug("[智能维度间相关性调整] 开始优化调整，目标相关性: {}, 强度: {:.3f}",
                 targetInterDimensionCorrelation, adjustmentIntensity);

        // 简化实现：轻微调整数据以影响维度间相关性
        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        // 根据强度添加微小的随机变异
        double variationLevel = 0.02 * adjustmentIntensity;
        Random random = new Random(42);

        for (List<Double> col : result) {
            for (int i = 0; i < col.size(); i++) {
                double variation = random.nextGaussian() * variationLevel;
                double newValue = col.get(i) + variation;
                newValue = Math.max(1.0, Math.min((double)currentScaleLevel, newValue));
                col.set(i, newValue);
            }
        }

        return result;
    }


    
    /**
     * 计算两个变量间的相关系数
     */
    private double calculateCorrelation(List<Double> var1, List<Double> var2) {
        if (var1.size() != var2.size() || var1.size() < 2) {
            return 0.0;
        }

        try {
            double[] array1 = var1.stream().mapToDouble(Double::doubleValue).toArray();
            double[] array2 = var2.stream().mapToDouble(Double::doubleValue).toArray();

            PearsonsCorrelation correlation = new PearsonsCorrelation();
            double corr = correlation.correlation(array1, array2);

            return Double.isNaN(corr) ? 0.0 : corr;
        } catch (Exception e) {
            return 0.0;
        }
    }


    /**
     * 计算Cronbach's Alpha系数 - 参考成功的实现
     */
    private double calculateCronbachAlpha(List<List<Double>> columnData) {
        if (columnData == null || columnData.isEmpty() || columnData.size() < 2) {
            log.warn("[信度计算] 数据无效或项目数少于2，返回0");
            return 0.0;
        }

        int itemCount = columnData.size();
        int sampleSize = columnData.get(0).size();

        // 验证所有列的样本数量一致
        for (List<Double> col : columnData) {
            if (col.size() != sampleSize) {
                log.warn("[信度计算] 数据列长度不一致，无法计算信度");
                return 0.0;
            }
        }

        // 检查数据有效性
        for (int i = 0; i < itemCount; i++) {
            List<Double> col = columnData.get(i);
            boolean hasValidData = col.stream().anyMatch(v -> v != null && !Double.isNaN(v) && Double.isFinite(v));
            if (!hasValidData) {
                log.warn("[信度计算] 第{}列没有有效数据", i + 1);
                return 0.0;
            }

            // 检查方差是否为0（所有值相同）
            DescriptiveStatistics colStats = new DescriptiveStatistics();
            col.forEach(value -> {
                if (value != null && !Double.isNaN(value) && Double.isFinite(value)) {
                    colStats.addValue(value);
                }
            });

            if (colStats.getVariance() <= 1e-10) {
                log.warn("[信度计算] 第{}列方差为0（所有值相同），信度无法计算", i + 1);
                return 0.0;
            }
        }

        // 计算总分
        List<Double> totalScores = new ArrayList<>();
        for (int i = 0; i < sampleSize; i++) {
            double total = 0.0;
            int validCount = 0;
            for (List<Double> col : columnData) {
                Double value = col.get(i);
                if (value != null && !Double.isNaN(value) && Double.isFinite(value)) {
                    total += value;
                    validCount++;
                }
            }
            if (validCount > 0) {
                totalScores.add(total);
            }
        }

        if (totalScores.size() < 2) {
            log.warn("[信度计算] 有效样本数少于2，无法计算信度");
            return 0.0;
        }

        // 计算总方差
        DescriptiveStatistics totalStats = new DescriptiveStatistics();
        totalScores.forEach(totalStats::addValue);
        double totalVariance = totalStats.getVariance();

        // 计算各项目方差和
        double itemVarianceSum = 0.0;
        for (List<Double> col : columnData) {
            DescriptiveStatistics itemStats = new DescriptiveStatistics();
            col.forEach(value -> {
                if (value != null && !Double.isNaN(value) && Double.isFinite(value)) {
                    itemStats.addValue(value);
                }
            });
            double variance = itemStats.getVariance();
            if (Double.isNaN(variance) || !Double.isFinite(variance)) {
                log.warn("[信度计算] 项目方差计算异常，返回0");
                return 0.0;
            }
            itemVarianceSum += variance;
        }

        // 计算Cronbach's Alpha
        if (totalVariance <= 1e-10 || Double.isNaN(totalVariance) || !Double.isFinite(totalVariance)) {
            log.warn("[信度计算] 总方差异常({})，返回0", totalVariance);
            return 0.0;
        }

        double alpha = (itemCount / (itemCount - 1.0)) * (1 - itemVarianceSum / totalVariance);

        // 检查计算结果
        if (Double.isNaN(alpha) || !Double.isFinite(alpha)) {
            log.warn("[信度计算] Alpha计算结果异常({})，返回0", alpha);
            return 0.0;
        }

        // 确保Alpha值在合理范围内
        double result = Math.max(0.0, Math.min(1.0, alpha));
        log.debug("[信度计算] 计算完成，Alpha={:.3f}", result);
        return result;
    }





    /**
     * 获取所有列号
     */
    private List<Integer> getAllColumns(List<List<Integer>> dimensions) {
        List<Integer> allColumns = new ArrayList<>();
        for (List<Integer> dimension : dimensions) {
            allColumns.addAll(dimension);
        }
        return allColumns;
    }

    /**
     * 获取数据索引
     */
    private int getDataIndex(Integer column, List<Integer> allColumns) {
        return allColumns.indexOf(column);
    }









    /**
     * 生成变更记录
     */
    private List<List<Object>> generateChangedCells(List<List<Double>> originalData,
            List<List<Double>> adjustedData, List<Integer> allColumns) {

        List<List<Object>> changedCells = new ArrayList<>();
        double threshold = 0.05; // 只有变化超过0.05才记录

        for (int colIdx = 0; colIdx < originalData.size(); colIdx++) {
            List<Double> originalCol = originalData.get(colIdx);
            List<Double> adjustedCol = adjustedData.get(colIdx);

            for (int rowIdx = 0; rowIdx < originalCol.size(); rowIdx++) {
                double originalValue = originalCol.get(rowIdx);
                double adjustedValue = adjustedCol.get(rowIdx);

                // 只有当变化足够大时才记录
                if (Math.abs(originalValue - adjustedValue) > threshold) {
                    // 根据原始数据的分布特征来决定最终值
                    String finalValue = generateReasonableValue(originalValue, adjustedValue, originalCol);

                    changedCells.add(Arrays.asList(
                        rowIdx + 1, // 行号（跳过表头）
                        allColumns.get(colIdx) - 1, // 列号（0基索引）
                        finalValue
                    ));
                }
            }
        }

        return changedCells;
    }

    /**
     * 根据原始数据分布生成合理的调整值
     */
    private String generateReasonableValue(double originalValue, double adjustedValue, List<Double> columnData) {
        // 使用当前设置的量表级数并确保调整后的值在合理范围内
        double finalValue = Math.max(1.0, Math.min((double)currentScaleLevel, adjustedValue));

        // 如果是量表数据，通常是整数值，但也可能有小数
        // 根据原始数据的特征来决定是否保留小数
        boolean hasDecimals = columnData.stream().anyMatch(v -> v % 1 != 0);

        if (hasDecimals) {
            // 如果原始数据有小数，保留一位小数
            return String.format("%.1f", finalValue);
        } else {
            // 如果原始数据都是整数，四舍五入到整数
            return String.valueOf(Math.round(finalValue));
        }
    }



    /**
     * 生成多维度调整说明 - 只包含表格中存在的数据
     */
    private String generateMultiDimensionalExplanation(List<List<Integer>> dimensions,
            List<Double> originalDimensionAlphas, List<Double> adjustedDimensionAlphas, List<Double> targetDimensionAlphas,
            Double originalTotalAlpha, Double adjustedTotalAlpha, Double targetTotalAlpha,
            Double originalKMO, Double adjustedKMO, Double targetKMO,
            int changedCellsCount, Map<String, Object> achievedMetrics) {

        StringBuilder explanation = new StringBuilder();
        explanation.append("成功完成分维度量表的信度效度调整：\n\n");

        // 各维度信度调整结果 - 只显示调整后的值、目标值、距离目标值和状态
        explanation.append("【维度信度调整结果】\n");
        for (int i = 0; i < dimensions.size(); i++) {
            List<Integer> dimension = dimensions.get(i);
            double adjustedAlpha = adjustedDimensionAlphas.get(i);
            double targetAlpha = targetDimensionAlphas.get(i);
            double distanceToTarget = adjustedAlpha - targetAlpha;
            String distanceStr = distanceToTarget >= 0 ? String.format("+%.3f", distanceToTarget) : String.format("%.3f", distanceToTarget);
            String status = Math.abs(distanceToTarget) <= 0.02 ? "✓达标" : "需优化";

            explanation.append(String.format("维度%d（题目%s）：调整后%.3f，目标%.3f，距离目标值%s，%s\n",
                i + 1, dimension.toString(), adjustedAlpha, targetAlpha, distanceStr, status));
        }

        // 总量表信度调整结果
        if (targetTotalAlpha != null && adjustedTotalAlpha != null) {
            double distanceToTarget = adjustedTotalAlpha - targetTotalAlpha;
            String distanceStr = distanceToTarget >= 0 ? String.format("+%.3f", distanceToTarget) : String.format("%.3f", distanceToTarget);
            String status = Math.abs(distanceToTarget) <= 0.02 ? "✓达标" : "需优化";
            explanation.append(String.format("\n【总量表信度】：调整后%.3f，目标%.3f，距离目标值%s，%s\n",
                adjustedTotalAlpha, targetTotalAlpha, distanceStr, status));
        }

        // KMO值调整结果（效度指标）
        if (targetKMO != null && adjustedKMO != null) {
            double distanceToTarget = adjustedKMO - targetKMO;
            String distanceStr = distanceToTarget >= 0 ? String.format("+%.3f", distanceToTarget) : String.format("%.3f", distanceToTarget);
            String status = Math.abs(distanceToTarget) <= 0.05 ? "✓达标" : "需优化";
            explanation.append(String.format("\n【效度指标KMO】：调整后%.3f，目标%.3f，距离目标值%s，%s\n",
                adjustedKMO, targetKMO, distanceStr, status));
        }

        // 调整统计信息 - 只显示表格中存在的统计数据
        explanation.append(String.format("\n【调整统计】\n"));
        explanation.append(String.format("- 调整单元格数量：%d个\n", changedCellsCount));
        explanation.append(String.format("- 涉及题目数量：%d个\n", getAllColumns(dimensions).size()));
        explanation.append(String.format("- 维度数量：%d个\n", dimensions.size()));

        // 质量评估 - 只显示表格中存在的达标比例
        explanation.append("\n【质量评估】\n");
        int passedDimensions = 0;
        for (int i = 0; i < adjustedDimensionAlphas.size(); i++) {
            if (Math.abs(adjustedDimensionAlphas.get(i) - targetDimensionAlphas.get(i)) <= 0.02) {
                passedDimensions++;
            }
        }
        explanation.append(String.format("- 达标维度比例：%d/%d\n", passedDimensions, dimensions.size()));

        explanation.append("\n调整完成，数据已按照目标要求进行优化。");

        return explanation.toString();
    }

    /**
     * 构建包含文字描述数据的表格摘要
     * 包含得分方向和均值变化信息
     */
    private Map<String, Object> buildSummaryMetrics(
            List<List<Integer>> dimensions,
            List<Double> originalDimensionAlphas, List<Double> adjustedDimensionAlphas, List<Double> targetDimensionAlphas,
            Double originalTotalAlpha, Double adjustedTotalAlpha, Double targetTotalAlpha,
            Double originalKMO, Double adjustedKMO, Double targetKMO,
            int changedCellsCount, List<List<Double>> targetItemMeans, List<List<String>> scoringDirections,
            List<List<Double>> adjustedData, List<Integer> allColumns) {

        Map<String, Object> metrics = new HashMap<>();

        // 基本信息
        metrics.put("dimensions", dimensions);
        metrics.put("cellsChanged", changedCellsCount);

        // 维度信度数据
        metrics.put("originalDimensionAlphas", originalDimensionAlphas);
        metrics.put("adjustedDimensionAlphas", adjustedDimensionAlphas);
        metrics.put("targetDimensionAlphas", targetDimensionAlphas);

        // 总量表信度数据
        if (originalTotalAlpha != null && adjustedTotalAlpha != null && targetTotalAlpha != null) {
            metrics.put("originalTotalAlpha", originalTotalAlpha);
            metrics.put("adjustedTotalAlpha", adjustedTotalAlpha);
            metrics.put("targetTotalAlpha", targetTotalAlpha);
        }

        // KMO数据
        if (originalKMO != null && adjustedKMO != null && targetKMO != null) {
            metrics.put("originalKMO", originalKMO);
            metrics.put("adjustedKMO", adjustedKMO);
            metrics.put("targetKMO", targetKMO);
        }

        // 构建详细的表格数据 - 包含所有调整信息
        List<List<Object>> summaryTableData = new ArrayList<>();

        // 添加维度信度行
        for (int i = 0; i < dimensions.size(); i++) {
            List<Integer> dimension = dimensions.get(i);
            double adjustedAlpha = adjustedDimensionAlphas.get(i);
            double targetAlpha = targetDimensionAlphas.get(i);
            double distanceToTarget = adjustedAlpha - targetAlpha;
            String distanceStr = distanceToTarget >= 0 ? String.format("+%.3f", distanceToTarget) : String.format("%.3f", distanceToTarget);
            String status = Math.abs(distanceToTarget) <= 0.02 ? "✓达标" : "需优化";

            summaryTableData.add(Arrays.asList(
                "维度" + (i + 1) + "信度（题目" + dimension.toString() + "）",
                String.format("%.3f", adjustedAlpha),
                String.format("%.3f", targetAlpha),
                distanceStr,
                status
            ));
        }

        // 添加得分方向设置信息
        if (scoringDirections != null && !scoringDirections.isEmpty()) {
            for (int i = 0; i < dimensions.size(); i++) {
                List<Integer> dimension = dimensions.get(i);
                List<String> dimDirections = scoringDirections.get(i);

                // 转换为中文显示
                String directionInfo = dimDirections.stream()
                    .map(dir -> "positive".equals(dir) ? "正向" : "反向")
                    .collect(java.util.stream.Collectors.joining(","));

                summaryTableData.add(Arrays.asList(
                    "维度" + (i + 1) + "得分方向（题目" + dimension.toString() + "）",
                    "-",
                    "-",
                    "-",
                    directionInfo
                ));
            }
        }

        // 添加题目均值信息（显示调整后的实际均值）
        for (int i = 0; i < dimensions.size(); i++) {
            List<Integer> dimension = dimensions.get(i);

            // 显示每个题目的实际均值（基于调整后但未反向转换的数据）
            for (int j = 0; j < dimension.size(); j++) {
                Integer col = dimension.get(j);
                String direction = scoringDirections != null ? scoringDirections.get(i).get(j) : "positive";

                // 计算调整后的实际均值（使用未反向转换的数据）
                int dataIdx = getDataIndex(col, allColumns);
                double actualMean = 0.0;
                if (dataIdx >= 0 && dataIdx < adjustedData.size()) {
                    List<Double> colData = adjustedData.get(dataIdx);
                    actualMean = colData.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
                }

                // 获取目标均值（如果有设置）
                Double targetMean = null;
                if (targetItemMeans != null && i < targetItemMeans.size() && j < targetItemMeans.get(i).size()) {
                    targetMean = targetItemMeans.get(i).get(j);
                }

                // 计算距离目标值的差距
                String distanceStr = "-";
                String status = direction.equals("negative") ? "反向化" : "正向";
                if (targetMean != null) {
                    double distance = actualMean - targetMean;
                    distanceStr = distance >= 0 ? String.format("+%.3f", distance) : String.format("%.3f", distance);
                    status = Math.abs(distance) <= 0.1 ? "✓达标" : "需优化";
                }

                summaryTableData.add(Arrays.asList(
                    String.format("题目%d均值（%s计分）", col, direction.equals("negative") ? "反向" : "正向"),
                    String.format("%.3f", actualMean),
                    targetMean != null ? String.format("%.3f", targetMean) : "-",
                    distanceStr,
                    status
                ));
            }
        }

        // 添加总量表信度行
        if (adjustedTotalAlpha != null && targetTotalAlpha != null) {
            double distanceToTarget = adjustedTotalAlpha - targetTotalAlpha;
            String distanceStr = distanceToTarget >= 0 ? String.format("+%.3f", distanceToTarget) : String.format("%.3f", distanceToTarget);
            String totalStatus = Math.abs(distanceToTarget) <= 0.02 ? "✓达标" : "需优化";

            summaryTableData.add(Arrays.asList(
                "总量表信度（全部题目）",
                String.format("%.3f", adjustedTotalAlpha),
                String.format("%.3f", targetTotalAlpha),
                distanceStr,
                totalStatus
            ));
        }

        // 添加KMO行
        if (adjustedKMO != null && targetKMO != null) {
            double distanceToTarget = adjustedKMO - targetKMO;
            String distanceStr = distanceToTarget >= 0 ? String.format("+%.3f", distanceToTarget) : String.format("%.3f", distanceToTarget);
            String kmoStatus = Math.abs(distanceToTarget) <= 0.05 ? "✓达标" : "需优化";

            summaryTableData.add(Arrays.asList(
                "KMO效度指标",
                String.format("%.3f", adjustedKMO),
                String.format("%.3f", targetKMO),
                distanceStr,
                kmoStatus
            ));
        }

        // 添加调整统计信息行
        summaryTableData.add(Arrays.asList(
            "调整单元格数量",
            "-",
            "-",
            "-",
            String.valueOf(changedCellsCount) + "个"
        ));

        summaryTableData.add(Arrays.asList(
            "涉及题目数量",
            "-",
            "-",
            "-",
            String.valueOf(getAllColumns(dimensions).size()) + "个"
        ));

        summaryTableData.add(Arrays.asList(
            "维度数量",
            "-",
            "-",
            "-",
            String.valueOf(dimensions.size()) + "个"
        ));

        // 计算达标情况
        int passedDimensions = 0;
        for (int i = 0; i < adjustedDimensionAlphas.size(); i++) {
            if (Math.abs(adjustedDimensionAlphas.get(i) - targetDimensionAlphas.get(i)) <= 0.02) {
                passedDimensions++;
            }
        }

        summaryTableData.add(Arrays.asList(
            "达标维度比例",
            "-",
            "-",
            "-",
            passedDimensions + "/" + dimensions.size()
        ));

        metrics.put("summaryTableData", summaryTableData);
        metrics.put("summaryTableHeaders", Arrays.asList("指标", "调整后", "目标值", "距离目标值", "状态"));

        return metrics;
    }
 
  
    /**
     * 基于调整后的数据计算KMO值 - 加强版，防止NaN
     */
    private double calculateKMOFromAdjustedData(List<List<Double>> adjustedData) {
        try {
            if (adjustedData == null || adjustedData.isEmpty() || adjustedData.size() < 2) {
                log.warn("[KMO计算] 需要至少2个变量才能计算KMO");
                return 0.0;
            }

            // 关键：在计算KMO之前，强制确保数据变异性
            List<List<Double>> safeData = ensureDataVariability(adjustedData);

            // 更严格的数据有效性检查
            for (int i = 0; i < safeData.size(); i++) {
                List<Double> col = safeData.get(i);
                if (col == null || col.isEmpty()) {
                    log.warn("[KMO计算] 第{}列数据为空", i + 1);
                    return 0.0;
                }

                // 检查是否所有值都相同（方差为0）- 更严格的阈值
                DescriptiveStatistics stats = new DescriptiveStatistics();
                for (Double value : col) {
                    if (value != null && !Double.isNaN(value) && Double.isFinite(value)) {
                        stats.addValue(value);
                    }
                }

                // 更严格的变异性要求
                if (stats.getN() < 3 || stats.getVariance() <= 1e-3 || stats.getStandardDeviation() <= 0.05) {
                    log.warn("[KMO计算] 第{}列变异性不足(N={}, 方差={:.6f}, 标准差={:.6f})，强制修复",
                            i + 1, stats.getN(), stats.getVariance(), stats.getStandardDeviation());
                    // 如果仍然有问题，返回一个安全的默认值
                    return 0.5;
                }

                // 检查唯一值数量
                Set<Double> uniqueValues = new HashSet<>();
                for (Double value : col) {
                    if (value != null && !Double.isNaN(value) && Double.isFinite(value)) {
                        uniqueValues.add(Math.round(value * 100.0) / 100.0);
                    }
                }

                if (uniqueValues.size() < 3) {
                    log.warn("[KMO计算] 第{}列只有{}个不同值，变异性不足", i + 1, uniqueValues.size());
                    return 0.5;
                }
            }

            // 使用修复后的数据进行后续计算
            adjustedData = safeData;

            // 标准化数据
            List<List<Double>> standardizedData = standardizeDataSafely(adjustedData);
            if (standardizedData == null) {
                log.warn("[KMO计算] 数据标准化失败");
                return 0.0;
            }

            // 计算相关矩阵
            RealMatrix correlationMatrix = calculateCorrelationMatrixSafely(standardizedData);
            if (correlationMatrix == null) {
                log.warn("[KMO计算] 相关矩阵计算失败");
                return 0.0;
            }

            // 计算KMO测度
            double kmo = calculateKMOMeasureSafely(correlationMatrix);
            log.debug("[KMO计算] 计算完成，KMO={:.3f}", kmo);
            return kmo;

        } catch (Exception e) {
            log.error("[KMO计算] 基于调整数据计算失败", e);
            return 0.0;
        }
    }

    /**
     * 安全的数据标准化
     */
    private List<List<Double>> standardizeDataSafely(List<List<Double>> data) {
        try {
            List<List<Double>> result = new ArrayList<>();

            for (List<Double> col : data) {
                DescriptiveStatistics stats = new DescriptiveStatistics();
                for (Double value : col) {
                    if (value != null && !Double.isNaN(value) && Double.isFinite(value)) {
                        stats.addValue(value);
                    }
                }

                double mean = stats.getMean();
                double std = stats.getStandardDeviation();

                if (std <= 1e-10 || Double.isNaN(std) || !Double.isFinite(std)) {
                    log.warn("[数据标准化] 标准差异常，无法标准化");
                    return null;
                }

                List<Double> standardizedCol = new ArrayList<>();
                for (Double value : col) {
                    if (value != null && !Double.isNaN(value) && Double.isFinite(value)) {
                        double standardized = (value - mean) / std;
                        if (Double.isNaN(standardized) || !Double.isFinite(standardized)) {
                            standardizedCol.add(0.0);
                        } else {
                            standardizedCol.add(standardized);
                        }
                    } else {
                        standardizedCol.add(0.0);
                    }
                }
                result.add(standardizedCol);
            }

            return result;
        } catch (Exception e) {
            log.error("[数据标准化] 标准化失败", e);
            return null;
        }
    }

    /**
     * 安全的相关矩阵计算
     */
    private RealMatrix calculateCorrelationMatrixSafely(List<List<Double>> data) {
        try {
            PearsonsCorrelation correlation = new PearsonsCorrelation();
            int n = data.size();
            double[][] corrMatrix = new double[n][n];

            for (int i = 0; i < n; i++) {
                for (int j = 0; j < n; j++) {
                    if (i == j) {
                        corrMatrix[i][j] = 1.0;
                    } else {
                        try {
                            double[] array1 = data.get(i).stream().mapToDouble(Double::doubleValue).toArray();
                            double[] array2 = data.get(j).stream().mapToDouble(Double::doubleValue).toArray();

                            double corr = correlation.correlation(array1, array2);
                            if (Double.isNaN(corr) || !Double.isFinite(corr)) {
                                corrMatrix[i][j] = 0.0;
                            } else {
                                corrMatrix[i][j] = corr;
                            }
                        } catch (Exception e) {
                            corrMatrix[i][j] = 0.0;
                        }
                    }
                }
            }

            return new Array2DRowRealMatrix(corrMatrix);
        } catch (Exception e) {
            log.error("[相关矩阵计算] 计算失败", e);
            return null;
        }
    }

    /**
     * 安全的KMO测度计算
     */
    private double calculateKMOMeasureSafely(RealMatrix correlationMatrix) {
        try {
            int n = correlationMatrix.getRowDimension();
            if (n < 2) {
                return 0.0;
            }

            // 计算反相关矩阵
            RealMatrix inverseMatrix;
            try {
                LUDecomposition lu = new LUDecomposition(correlationMatrix);
                if (lu.getDeterminant() == 0) {
                    log.warn("[KMO计算] 相关矩阵不可逆");
                    return 0.0;
                }
                inverseMatrix = lu.getSolver().getInverse();
            } catch (Exception e) {
                log.warn("[KMO计算] 矩阵求逆失败");
                return 0.0;
            }

            double sumR2 = 0.0;
            double sumA2 = 0.0;

            for (int i = 0; i < n; i++) {
                for (int j = 0; j < n; j++) {
                    if (i != j) {
                        double r = correlationMatrix.getEntry(i, j);
                        double a = -inverseMatrix.getEntry(i, j) / Math.sqrt(inverseMatrix.getEntry(i, i) * inverseMatrix.getEntry(j, j));

                        if (Double.isFinite(r)) {
                            sumR2 += r * r;
                        }
                        if (Double.isFinite(a)) {
                            sumA2 += a * a;
                        }
                    }
                }
            }

            if (sumR2 + sumA2 <= 1e-10) {
                return 0.0;
            }

            double kmo = sumR2 / (sumR2 + sumA2);

            if (Double.isNaN(kmo) || !Double.isFinite(kmo)) {
                return 0.0;
            }

            return Math.max(0.0, Math.min(1.0, kmo));

        } catch (Exception e) {
            log.error("[KMO测度计算] 计算失败", e);
            return 0.0;
        }
    }
 
   

    /**
     * 获取题目的量表级数（优先从缓存获取，否则使用默认值）
     * @param questionNum 题目编号
     * @param data 题目数据（暂时不使用，保留接口兼容性）
     * @return 量表级数
     */
    private Integer getQuestionScaleLevel(Integer questionNum, List<Double> data) {
        // 优先从缓存获取
        Integer scaleLevel = questionScaleLevelsCache.get(questionNum);
        if (scaleLevel != null) {
            return scaleLevel;
        }

        // 如果缓存中没有，使用当前设置的量表级数
        scaleLevel = currentScaleLevel;

        // 将默认值保存到缓存中
        questionScaleLevelsCache.put(questionNum, scaleLevel);

        return scaleLevel;
    }

    /**
     * 生成分维度调整量表的配置文本JSON
     */
    private String generateConfigText(List<List<Integer>> dimensions, Integer scaleLevel, List<Double> targetDimensionAlphas,
                                    Double targetTotalAlpha, Double targetKMO, Double targetInterDimensionCorrelation,
                                    Double tolerance, List<List<Double>> targetItemMeans, List<List<String>> scoringDirections,
                                    Map<Integer, Integer> columnToQuestionMap, Map<Integer, SurveyData> questionDataMap) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            Map<String, Object> config = new HashMap<>();

            // 基本信息
            config.put("configType", "multiDimensionalScale");
            config.put("description", "分维度调整量表配置");
            config.put("version", "1.0");
            // 使用ISO格式的时间戳
            config.put("timestamp", java.time.Instant.now().toString());

            // 全局参数
            Map<String, Object> globalParams = new HashMap<>();
            globalParams.put("scaleLevel", scaleLevel != null ? scaleLevel : 5);
            globalParams.put("targetTotalAlpha", targetTotalAlpha != null ? targetTotalAlpha : 0.85);
            globalParams.put("targetKMO", targetKMO != null ? targetKMO : 0.8);
            globalParams.put("targetInterDimensionCorrelation", targetInterDimensionCorrelation != null ? targetInterDimensionCorrelation : 0.4);
            globalParams.put("tolerance", tolerance != null ? tolerance : 0.02);
            config.put("globalParams", globalParams);

            // 维度配置
            List<Map<String, Object>> dimensionConfigs = new ArrayList<>();
            for (int i = 0; i < dimensions.size(); i++) {
                Map<String, Object> dimensionConfig = new HashMap<>();
                dimensionConfig.put("name", "维度" + (i + 1));
                dimensionConfig.put("columns", dimensions.get(i));
                dimensionConfig.put("targetAlpha", targetDimensionAlphas != null && i < targetDimensionAlphas.size() ?
                    targetDimensionAlphas.get(i) : 0.8);

                // 题目详细信息
                List<Map<String, Object>> questionDetails = new ArrayList<>();
                List<Integer> dimensionColumns = dimensions.get(i);
                for (int j = 0; j < dimensionColumns.size(); j++) {
                    Integer colIndex = dimensionColumns.get(j);
                    Integer questionId = columnToQuestionMap.get(colIndex);

                    Map<String, Object> questionDetail = new HashMap<>();

                    if (questionId != null && questionDataMap.containsKey(questionId)) {
                        SurveyData questionData = questionDataMap.get(questionId);

                        // 判断是否为矩阵题的子题目
                        if (questionData.getSubQuestions() != null && !questionData.getSubQuestions().isEmpty()) {
                            // 矩阵题：找到对应的子题目索引
                            int subIndex = -1;
                            if (questionData.getColIndices() != null) {
                                for (int k = 0; k < questionData.getColIndices().size(); k++) {
                                    if (questionData.getColIndices().get(k).equals(colIndex)) {
                                        subIndex = k;
                                        break;
                                    }
                                }
                            }

                            questionDetail.put("type", "subquestion");
                            questionDetail.put("mainQuestionId", questionId);
                            questionDetail.put("subIndex", subIndex);
                        } else {
                            // 普通题目
                            questionDetail.put("type", "question");
                            questionDetail.put("questionId", questionId);
                        }
                    } else {
                        // 如果找不到对应的题目，使用默认值
                        questionDetail.put("type", "subquestion");
                        questionDetail.put("mainQuestionId", i + 1);
                        questionDetail.put("subIndex", j);
                    }

                    questionDetail.put("colIndex", colIndex);

                    // 得分方向
                    String scoringDirection = "positive"; // 默认正向
                    if (scoringDirections != null && i < scoringDirections.size() &&
                        j < scoringDirections.get(i).size()) {
                        scoringDirection = scoringDirections.get(i).get(j);
                    }
                    questionDetail.put("scoringDirection", scoringDirection);

                    // 目标均值
                    Object targetMean = null;
                    if (targetItemMeans != null && i < targetItemMeans.size() &&
                        j < targetItemMeans.get(i).size() && targetItemMeans.get(i).get(j) != null) {
                        // 转换为整数（如果是整数值）或保持为小数
                        Double meanValue = targetItemMeans.get(i).get(j);
                        if (meanValue != null) {
                            if (meanValue == meanValue.intValue()) {
                                targetMean = meanValue.intValue();
                            } else {
                                targetMean = meanValue;
                            }
                        }
                    }
                    questionDetail.put("targetMean", targetMean);

                    questionDetails.add(questionDetail);
                }
                dimensionConfig.put("questionDetails", questionDetails);

                dimensionConfigs.add(dimensionConfig);
            }
            config.put("dimensions", dimensionConfigs);

            return objectMapper.writeValueAsString(config);

        } catch (Exception e) {
            log.error("[配置文本生成] 生成失败", e);
            return null;
        }
    }

    /**
     * 验证题目类型是否适用于分维度量表调整
     * @param questionNums 题目编号集合
     * @param questionTypesCache 题目类型缓存
     */
    private void validateQuestionTypesForMultiDimensional(Set<Integer> questionNums, Map<Integer, String> questionTypesCache) {
        // 分维度量表调整只支持特定题型
        Set<String> allowedTypes = Set.of("3", "5", "6single");
        List<String> invalidQuestions = new ArrayList<>();

        for (Integer questionNum : questionNums) {
            String questionType = questionTypesCache.get(questionNum);
            if (questionType == null) {
                log.warn("[分维度量表调整] 题目{}的类型信息缺失，跳过验证", questionNum);
                continue;
            }

            if (!allowedTypes.contains(questionType)) {
                String typeDescription = getQuestionTypeDescription(questionType);
                invalidQuestions.add(String.format("Q%d(%s)", questionNum, typeDescription));
            }
        }

        if (!invalidQuestions.isEmpty()) {
            throw new IllegalArgumentException(String.format(
                "分维度量表调整只支持以下题型：单选题(类型3)、单项量表题(类型5)、矩阵单选题(类型6single)。" +
                "以下题目类型不支持：%s", String.join(", ", invalidQuestions)
            ));
        }

        log.info("[分维度量表调整] 题目类型验证通过，所有题目均为支持的类型");
    }

    /**
     * 验证量表级数一致性
     * @param questionNums 题目编号集合
     * @param questionScaleLevelsMap 题目量表级数映射
     */
    private void validateScaleConsistency(Set<Integer> questionNums, Map<Integer, Integer> questionScaleLevelsMap) {
        Map<Integer, List<Integer>> scaleLevelGroups = new HashMap<>();

        for (Integer questionNum : questionNums) {
            Integer scaleLevel = questionScaleLevelsMap.get(questionNum);
            if (scaleLevel == null) {
                log.warn("[分维度量表调整] 题目{}的量表级数信息缺失，跳过验证", questionNum);
                continue; // 跳过不存在的题目，不参与验证
            }

            scaleLevelGroups.computeIfAbsent(scaleLevel, k -> new ArrayList<>()).add(questionNum);
        }

        if (scaleLevelGroups.size() > 1) {
            List<String> details = new ArrayList<>();
            for (Map.Entry<Integer, List<Integer>> entry : scaleLevelGroups.entrySet()) {
                List<String> questionList = entry.getValue().stream()
                    .map(q -> "Q" + q)
                    .collect(Collectors.toList());
                details.add(String.format("%d级量表: %s", entry.getKey(), String.join(", ", questionList)));
            }

            throw new IllegalArgumentException(String.format(
                "分维度量表调整要求所有题目的量表级数必须一致，但检测到以下不一致：%s。" +
                "建议：请确保所有参与分析的题目使用相同的量表级数（如都是5级量表或都是7级量表）。",
                String.join("；", details)
            ));
        }

        Integer mainScaleLevel = scaleLevelGroups.keySet().iterator().next();
        log.info("[分维度量表调整] 量表级数一致性验证通过，所有题目均为{}级量表", mainScaleLevel);
    }

    /**
     * 获取题目类型描述
     * @param type 题目类型
     * @return 类型描述
     */
    private String getQuestionTypeDescription(String type) {
        Map<String, String> typeMap = Map.of(
            "1", "填空题",
            "2", "填空题",
            "3", "单选题",
            "4", "多选题",
            "5", "单项量表题",
            "6single", "矩阵单选题",
            "6multiple", "矩阵多选题",
            "7", "下拉题",
            "8", "单项滑条题",
            "11", "排序题"
        );
        return typeMap.getOrDefault(type, "未知题型");
    }

}
